<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>魂斗罗小游戏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: 'Courier New', monospace;
            overflow: hidden;
        }

        .game-container {
            position: relative;
            border: 3px solid #00ff00;
            border-radius: 10px;
            background: linear-gradient(45deg, #001100, #003300);
        }

        canvas {
            display: block;
            background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 40%, #228B22 100%);
        }

        .ui-overlay {
            position: absolute;
            top: 10px;
            left: 10px;
            color: #00ff00;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 2px 2px 4px #000;
            z-index: 10;
        }

        .game-over {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #ff0000;
            font-size: 36px;
            font-weight: bold;
            text-shadow: 3px 3px 6px #000;
            text-align: center;
            z-index: 20;
            display: none;
        }

        .controls {
            position: absolute;
            bottom: 10px;
            left: 10px;
            color: #00ff00;
            font-size: 12px;
            text-shadow: 1px 1px 2px #000;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        <div class="ui-overlay">
            <div>生命: <span id="lives">3</span></div>
            <div>分数: <span id="score">0</span></div>
            <div>等级: <span id="level">1</span></div>
        </div>
        <div class="game-over" id="gameOver">
            <div>游戏结束!</div>
            <div style="font-size: 18px; margin-top: 10px;">按 R 重新开始</div>
        </div>
        <div class="controls">
            WASD: 移动 | 空格: 射击 | R: 重新开始
        </div>
    </div>

    <script>
        class ContraGame {
            constructor() {
                this.canvas = document.getElementById('gameCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.width = this.canvas.width;
                this.height = this.canvas.height;
                
                // 游戏状态
                this.gameState = 'playing'; // playing, gameOver
                this.score = 0;
                this.lives = 3;
                this.level = 1;
                
                // 输入处理
                this.keys = {};
                this.setupEventListeners();
                
                // 游戏对象
                this.player = new Player(100, this.height - 100);
                this.bullets = [];
                this.enemies = [];
                this.explosions = [];
                
                // 游戏循环
                this.lastTime = 0;
                this.enemySpawnTimer = 0;
                this.enemySpawnInterval = 2000; // 2秒生成一个敌人
                
                this.gameLoop();
            }
            
            setupEventListeners() {
                document.addEventListener('keydown', (e) => {
                    this.keys[e.key.toLowerCase()] = true;
                    if (e.key === ' ') {
                        e.preventDefault();
                        this.player.shoot();
                    }
                    if (e.key.toLowerCase() === 'r' && this.gameState === 'gameOver') {
                        this.restart();
                    }
                });
                
                document.addEventListener('keyup', (e) => {
                    this.keys[e.key.toLowerCase()] = false;
                });
            }
            
            gameLoop(currentTime = 0) {
                const deltaTime = currentTime - this.lastTime;
                this.lastTime = currentTime;
                
                if (this.gameState === 'playing') {
                    this.update(deltaTime);
                }
                this.render();
                
                requestAnimationFrame((time) => this.gameLoop(time));
            }
            
            update(deltaTime) {
                // 更新玩家
                this.player.update(this.keys, deltaTime);
                
                // 更新子弹
                this.bullets = this.bullets.filter(bullet => {
                    bullet.update(deltaTime);
                    return bullet.x < this.width && bullet.x > 0 && 
                           bullet.y < this.height && bullet.y > 0;
                });
                
                // 生成敌人
                this.enemySpawnTimer += deltaTime;
                if (this.enemySpawnTimer >= this.enemySpawnInterval) {
                    this.spawnEnemy();
                    this.enemySpawnTimer = 0;
                }
                
                // 更新敌人
                this.enemies.forEach(enemy => enemy.update(deltaTime));
                this.enemies = this.enemies.filter(enemy => enemy.x > -50);
                
                // 更新爆炸效果
                this.explosions.forEach(explosion => explosion.update(deltaTime));
                this.explosions = this.explosions.filter(explosion => !explosion.finished);
                
                // 碰撞检测
                this.checkCollisions();
                
                // 更新UI
                this.updateUI();
            }
            
            spawnEnemy() {
                const types = ['soldier', 'tank'];
                const type = types[Math.floor(Math.random() * types.length)];
                const y = Math.random() * (this.height - 150) + 50;
                this.enemies.push(new Enemy(this.width, y, type));
            }
            
            checkCollisions() {
                // 子弹击中敌人
                for (let i = this.bullets.length - 1; i >= 0; i--) {
                    const bullet = this.bullets[i];
                    for (let j = this.enemies.length - 1; j >= 0; j--) {
                        const enemy = this.enemies[j];
                        if (this.isColliding(bullet, enemy)) {
                            // 创建爆炸效果
                            this.explosions.push(new Explosion(enemy.x, enemy.y));
                            
                            // 移除子弹和敌人
                            this.bullets.splice(i, 1);
                            this.enemies.splice(j, 1);
                            
                            // 增加分数
                            this.score += enemy.type === 'tank' ? 200 : 100;
                            break;
                        }
                    }
                }
                
                // 敌人撞击玩家
                for (let i = this.enemies.length - 1; i >= 0; i--) {
                    const enemy = this.enemies[i];
                    if (this.isColliding(this.player, enemy)) {
                        this.explosions.push(new Explosion(this.player.x, this.player.y));
                        this.enemies.splice(i, 1);
                        this.lives--;
                        
                        if (this.lives <= 0) {
                            this.gameState = 'gameOver';
                            document.getElementById('gameOver').style.display = 'block';
                        }
                    }
                }
            }
            
            isColliding(obj1, obj2) {
                return obj1.x < obj2.x + obj2.width &&
                       obj1.x + obj1.width > obj2.x &&
                       obj1.y < obj2.y + obj2.height &&
                       obj1.y + obj1.height > obj2.y;
            }
            
            updateUI() {
                document.getElementById('lives').textContent = this.lives;
                document.getElementById('score').textContent = this.score;
                document.getElementById('level').textContent = this.level;
            }
            
            restart() {
                this.gameState = 'playing';
                this.score = 0;
                this.lives = 3;
                this.level = 1;
                this.player = new Player(100, this.height - 100);
                this.bullets = [];
                this.enemies = [];
                this.explosions = [];
                this.enemySpawnTimer = 0;
                document.getElementById('gameOver').style.display = 'none';
            }
            
            render() {
                // 清空画布
                this.ctx.clearRect(0, 0, this.width, this.height);
                
                // 绘制背景
                this.drawBackground();
                
                // 绘制游戏对象
                this.player.render(this.ctx);
                this.bullets.forEach(bullet => bullet.render(this.ctx));
                this.enemies.forEach(enemy => enemy.render(this.ctx));
                this.explosions.forEach(explosion => explosion.render(this.ctx));
            }
            
            drawBackground() {
                // 绘制地面
                this.ctx.fillStyle = '#8B4513';
                this.ctx.fillRect(0, this.height - 50, this.width, 50);
                
                // 绘制一些装饰性元素
                this.ctx.fillStyle = '#006400';
                for (let i = 0; i < 10; i++) {
                    const x = i * 80 + Math.sin(Date.now() * 0.001 + i) * 10;
                    const y = this.height - 60;
                    this.ctx.fillRect(x, y, 20, 15);
                }
            }
        }
        
        class Player {
            constructor(x, y) {
                this.x = x;
                this.y = y;
                this.width = 30;
                this.height = 40;
                this.speed = 200;
                this.shootCooldown = 0;
                this.shootInterval = 200;
            }
            
            update(keys, deltaTime) {
                // 移动控制
                if (keys['a'] || keys['arrowleft']) {
                    this.x -= this.speed * deltaTime / 1000;
                }
                if (keys['d'] || keys['arrowright']) {
                    this.x += this.speed * deltaTime / 1000;
                }
                if (keys['w'] || keys['arrowup']) {
                    this.y -= this.speed * deltaTime / 1000;
                }
                if (keys['s'] || keys['arrowdown']) {
                    this.y += this.speed * deltaTime / 1000;
                }
                
                // 边界检测
                this.x = Math.max(0, Math.min(game.width - this.width, this.x));
                this.y = Math.max(0, Math.min(game.height - this.height, this.y));
                
                // 更新射击冷却
                if (this.shootCooldown > 0) {
                    this.shootCooldown -= deltaTime;
                }
            }
            
            shoot() {
                if (this.shootCooldown <= 0) {
                    game.bullets.push(new Bullet(this.x + this.width, this.y + this.height / 2, 1));
                    this.shootCooldown = this.shootInterval;
                }
            }
            
            render(ctx) {
                // 绘制玩家身体
                ctx.fillStyle = '#0066cc';
                ctx.fillRect(this.x, this.y, this.width, this.height);
                
                // 绘制头部
                ctx.fillStyle = '#ffcc99';
                ctx.fillRect(this.x + 5, this.y - 10, 20, 15);
                
                // 绘制武器
                ctx.fillStyle = '#333';
                ctx.fillRect(this.x + this.width, this.y + this.height / 2 - 2, 15, 4);
            }
        }
        
        class Bullet {
            constructor(x, y, direction) {
                this.x = x;
                this.y = y;
                this.width = 8;
                this.height = 3;
                this.speed = 400;
                this.direction = direction; // 1 for right, -1 for left
            }
            
            update(deltaTime) {
                this.x += this.speed * this.direction * deltaTime / 1000;
            }
            
            render(ctx) {
                ctx.fillStyle = '#ffff00';
                ctx.fillRect(this.x, this.y, this.width, this.height);
                
                // 子弹尾迹效果
                ctx.fillStyle = '#ffaa00';
                ctx.fillRect(this.x - 5, this.y + 1, 5, 1);
            }
        }
        
        class Enemy {
            constructor(x, y, type) {
                this.x = x;
                this.y = y;
                this.type = type;
                this.speed = type === 'tank' ? 50 : 80;
                
                if (type === 'tank') {
                    this.width = 50;
                    this.height = 30;
                } else {
                    this.width = 25;
                    this.height = 35;
                }
            }
            
            update(deltaTime) {
                this.x -= this.speed * deltaTime / 1000;
            }
            
            render(ctx) {
                if (this.type === 'tank') {
                    // 绘制坦克
                    ctx.fillStyle = '#666';
                    ctx.fillRect(this.x, this.y, this.width, this.height);
                    ctx.fillStyle = '#444';
                    ctx.fillRect(this.x + 5, this.y + 5, this.width - 10, this.height - 10);
                    // 炮管
                    ctx.fillStyle = '#333';
                    ctx.fillRect(this.x - 15, this.y + this.height / 2 - 2, 20, 4);
                } else {
                    // 绘制士兵
                    ctx.fillStyle = '#cc0000';
                    ctx.fillRect(this.x, this.y, this.width, this.height);
                    // 头部
                    ctx.fillStyle = '#ffcc99';
                    ctx.fillRect(this.x + 5, this.y - 8, 15, 12);
                    // 武器
                    ctx.fillStyle = '#333';
                    ctx.fillRect(this.x - 10, this.y + this.height / 2 - 1, 12, 2);
                }
            }
        }
        
        class Explosion {
            constructor(x, y) {
                this.x = x;
                this.y = y;
                this.particles = [];
                this.duration = 500;
                this.age = 0;
                this.finished = false;
                
                // 创建粒子
                for (let i = 0; i < 8; i++) {
                    this.particles.push({
                        x: x,
                        y: y,
                        vx: (Math.random() - 0.5) * 200,
                        vy: (Math.random() - 0.5) * 200,
                        life: 1
                    });
                }
            }
            
            update(deltaTime) {
                this.age += deltaTime;
                
                if (this.age >= this.duration) {
                    this.finished = true;
                    return;
                }
                
                this.particles.forEach(particle => {
                    particle.x += particle.vx * deltaTime / 1000;
                    particle.y += particle.vy * deltaTime / 1000;
                    particle.life = 1 - (this.age / this.duration);
                });
            }
            
            render(ctx) {
                this.particles.forEach(particle => {
                    const alpha = particle.life;
                    const size = 4 * alpha;
                    
                    ctx.save();
                    ctx.globalAlpha = alpha;
                    ctx.fillStyle = `hsl(${Math.random() * 60}, 100%, 50%)`;
                    ctx.fillRect(particle.x - size/2, particle.y - size/2, size, size);
                    ctx.restore();
                });
            }
        }
        
        // 启动游戏
        const game = new ContraGame();
    </script>
</body>
</html>
