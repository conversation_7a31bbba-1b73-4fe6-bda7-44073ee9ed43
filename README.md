# 3D污水处理工艺流程可视化系统

一个基于Three.js构建的沉浸式3D污水处理厂可视化系统，提供实时数据监控、设备状态管理和工艺流程展示。

## 🌟 主要特性

### 🏭 完整的污水处理工艺流程
- **预处理单元**: 格栅池、沉砂池
- **生物处理单元**: A²/O工艺生化池
- **沉淀单元**: 初沉池、二沉池
- **深度处理单元**: 过滤、UV消毒、化学消毒
- **辅助设备**: 提升泵站、管道系统

### 🎮 交互式3D可视化
- 真实感3D设备模型
- 动态粒子效果（水流、气泡、沉淀）
- 设备运行动画
- 多视角切换（总览、细节、俯视）
- 鼠标交互和设备信息展示

### 📊 实时数据监控
- COD、BOD、SS、氨氮、总磷等水质参数
- 设备运行状态和效率
- 历史数据趋势分析
- 报警系统和阈值监控

### 🎨 丰富的视觉效果
- 动态光照系统（日夜变化、天气效果）
- 高质量阴影和反射
- 粒子系统（气泡、水流、沉淀物）
- 设备运行动画

### 🔊 音效系统
- 环境音效（水流声、机械运转声）
- 设备操作音效
- 报警提示音
- 音量控制和可视化

## 🏗️ 项目结构

```
src/
├── main.js                 # 主入口文件
├── scene/                  # 场景管理
│   ├── SceneManager.js     # 场景管理器
│   ├── LightingManager.js  # 光照管理器
│   └── CameraController.js # 相机控制器
├── models/                 # 设备模型
│   ├── Equipment.js        # 设备基类
│   ├── EquipmentManager.js # 设备管理器
│   ├── GratingPool.js      # 格栅池
│   ├── GritChamber.js      # 沉砂池
│   ├── LiftPump.js         # 提升泵站
│   ├── PrimaryClarifier.js # 初沉池
│   ├── BiologicalTank.js   # 生化池
│   ├── SecondaryClarifier.js # 二沉池
│   ├── AdvancedTreatment.js # 深度处理
│   └── PipelineSystem.js   # 管道系统
├── effects/                # 特效系统
│   ├── ParticleSystem.js   # 粒子系统
│   └── AnimationManager.js # 动画管理器
├── ui/                     # 用户界面
│   └── UIManager.js        # UI管理器
└── utils/                  # 工具类
    ├── DataManager.js      # 数据管理器
    ├── EventHandler.js     # 事件处理器
    └── AudioManager.js     # 音频管理器
```

## 🚀 快速开始

### 环境要求
- Node.js 16+
- 现代浏览器（支持WebGL 2.0）

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 🎯 核心功能

### 设备管理
- **设备状态控制**: 启动/停止/重置设备
- **参数调节**: 实时调整设备运行参数
- **故障模拟**: 模拟设备故障和维护状态
- **效率监控**: 实时监控设备运行效率

### 数据可视化
- **实时图表**: 显示关键水质参数变化趋势
- **历史数据**: 查看历史数据和统计分析
- **报警管理**: 参数超标报警和处理记录
- **数据导出**: 支持CSV和JSON格式数据导出

### 工艺流程
- **A²/O工艺**: 完整的厌氧-缺氧-好氧生物处理工艺
- **深度处理**: 过滤、UV消毒、化学消毒工艺
- **污泥处理**: 污泥回流和剩余污泥排放
- **自动控制**: 基于水质参数的自动控制逻辑

## 🛠️ 技术栈

### 核心技术
- **Three.js**: 3D图形渲染引擎
- **WebGL**: 硬件加速图形API
- **JavaScript ES6+**: 现代JavaScript语法

### 开发工具
- **Vite**: 快速构建工具
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化

### 浏览器支持
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 📱 用户界面

### 控制面板
- 系统启动/停止控制
- 视角切换按钮
- 显示选项开关
- 时间和天气控制

### 数据面板
- 实时水质参数显示
- 小型趋势图表
- 处理效率指标
- 报警状态显示

### 信息面板
- 设备详细信息
- 运行参数显示
- 操作控制按钮
- 状态历史记录

## 🎮 操作指南

### 基本操作
- **鼠标左键**: 旋转视角
- **鼠标右键**: 平移视角
- **滚轮**: 缩放视角
- **点击设备**: 查看设备信息
- **ESC键**: 取消选择

### 快捷键
- **Ctrl+H**: 隐藏/显示UI面板
- **Ctrl+R**: 重置视角
- **空格键**: 暂停/继续动画

## 🔧 配置选项

### 渲染设置
```javascript
// 在main.js中配置
const renderConfig = {
    antialias: true,        // 抗锯齿
    shadows: true,          // 阴影
    particleCount: 1000,    // 粒子数量
    animationSpeed: 1.0     // 动画速度
};
```

### 数据更新频率
```javascript
// 在DataManager.js中配置
const dataConfig = {
    updateInterval: 2000,   // 数据更新间隔(ms)
    historyLimit: 1000,     // 历史数据点数
    alarmThresholds: {      // 报警阈值
        cod: 150,
        bod: 100,
        ss: 60
    }
};
```

## 🐛 故障排除

### 常见问题

**Q: 页面加载缓慢或卡顿**
A: 检查浏览器是否支持WebGL 2.0，降低粒子数量或关闭阴影效果

**Q: 音效无法播放**
A: 现代浏览器需要用户交互后才能播放音频，点击页面任意位置激活音频

**Q: 设备模型显示异常**
A: 检查浏览器控制台错误信息，可能是WebGL上下文丢失

**Q: 数据不更新**
A: 检查DataManager是否正常启动，查看控制台错误信息

### 性能优化
- 降低粒子数量
- 关闭阴影效果
- 减少同时显示的设备数量
- 使用较低的渲染分辨率

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

### 开发流程
1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

### 代码规范
- 使用ESLint检查代码质量
- 遵循JavaScript Standard Style
- 添加适当的注释和文档

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

- Three.js团队提供的优秀3D引擎
- 污水处理工艺专业知识参考
- 开源社区的支持和贡献

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者

---

**注意**: 本系统仅用于教育和演示目的，实际污水处理厂的设计和运行需要专业工程师指导。
