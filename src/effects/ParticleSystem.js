/**
 * 粒子系统 - 管理所有粒子效果
 */

import * as THREE from 'three';

export class ParticleSystem {
    constructor(scene) {
        this.scene = scene;
        this.particleGroups = new Map();
        this.isVisible = true;
        this.globalIntensity = 1.0;
    }

    // 创建水流粒子效果
    createWaterFlow(config) {
        const {
            name,
            position = new THREE.Vector3(0, 0, 0),
            count = 200,
            spread = new THREE.Vector3(2, 1, 2),
            velocity = new THREE.Vector3(1, 0, 0),
            color = 0x006994,
            size = 0.05,
            opacity = 0.6
        } = config;

        const geometry = new THREE.BufferGeometry();
        const positions = new Float32Array(count * 3);
        const velocities = new Float32Array(count * 3);
        const colors = new Float32Array(count * 3);
        const sizes = new Float32Array(count);
        const lifetimes = new Float32Array(count);

        for (let i = 0; i < count; i++) {
            const i3 = i * 3;

            // 初始位置
            positions[i3] = position.x + (Math.random() - 0.5) * spread.x;
            positions[i3 + 1] = position.y + (Math.random() - 0.5) * spread.y;
            positions[i3 + 2] = position.z + (Math.random() - 0.5) * spread.z;

            // 速度
            velocities[i3] = velocity.x + (Math.random() - 0.5) * 0.2;
            velocities[i3 + 1] = velocity.y + (Math.random() - 0.5) * 0.1;
            velocities[i3 + 2] = velocity.z + (Math.random() - 0.5) * 0.2;

            // 颜色
            const particleColor = new THREE.Color(color);
            colors[i3] = particleColor.r;
            colors[i3 + 1] = particleColor.g;
            colors[i3 + 2] = particleColor.b;

            // 大小和生命周期
            sizes[i] = size + Math.random() * size * 0.5;
            lifetimes[i] = Math.random() * 10; // 0-10秒生命周期
        }

        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
        geometry.setAttribute('lifetime', new THREE.BufferAttribute(lifetimes, 1));

        const material = new THREE.PointsMaterial({
            size: size,
            transparent: true,
            opacity: opacity,
            vertexColors: true,
            blending: THREE.AdditiveBlending,
            depthWrite: false
        });

        const particles = new THREE.Points(geometry, material);
        particles.userData = {
            type: 'waterFlow',
            config: config,
            originalPosition: position.clone(),
            isActive: true
        };

        this.scene.add(particles);
        this.particleGroups.set(name, particles);

        return particles;
    }

    // 创建气泡效果
    createBubbles(config) {
        const {
            name,
            position = new THREE.Vector3(0, 0, 0),
            count = 100,
            spread = new THREE.Vector3(1, 0.1, 1),
            riseSpeed = 1.0,
            size = 0.03,
            opacity = 0.8
        } = config;

        const geometry = new THREE.BufferGeometry();
        const positions = new Float32Array(count * 3);
        const velocities = new Float32Array(count * 3);
        const colors = new Float32Array(count * 3);
        const sizes = new Float32Array(count);

        for (let i = 0; i < count; i++) {
            const i3 = i * 3;

            // 初始位置
            positions[i3] = position.x + (Math.random() - 0.5) * spread.x;
            positions[i3 + 1] = position.y + Math.random() * spread.y;
            positions[i3 + 2] = position.z + (Math.random() - 0.5) * spread.z;

            // 上升速度
            velocities[i3] = (Math.random() - 0.5) * 0.1;
            velocities[i3 + 1] = riseSpeed + Math.random() * riseSpeed * 0.5;
            velocities[i3 + 2] = (Math.random() - 0.5) * 0.1;

            // 白色气泡
            colors[i3] = 1;
            colors[i3 + 1] = 1;
            colors[i3 + 2] = 1;

            // 大小
            sizes[i] = size + Math.random() * size * 0.5;
        }

        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

        const material = new THREE.PointsMaterial({
            size: size,
            transparent: true,
            opacity: opacity,
            vertexColors: true,
            blending: THREE.AdditiveBlending
        });

        const particles = new THREE.Points(geometry, material);
        particles.userData = {
            type: 'bubbles',
            config: config,
            originalPosition: position.clone(),
            isActive: true
        };

        this.scene.add(particles);
        this.particleGroups.set(name, particles);

        return particles;
    }

    // 创建污泥沉降效果
    createSludgeSedimentation(config) {
        const {
            name,
            position = new THREE.Vector3(0, 0, 0),
            count = 300,
            spread = new THREE.Vector3(3, 2, 3),
            settlingSpeed = 0.2,
            color = 0x8b4513,
            size = 0.04,
            opacity = 0.7
        } = config;

        const geometry = new THREE.BufferGeometry();
        const positions = new Float32Array(count * 3);
        const velocities = new Float32Array(count * 3);
        const colors = new Float32Array(count * 3);
        const sizes = new Float32Array(count);

        for (let i = 0; i < count; i++) {
            const i3 = i * 3;

            // 随机分布
            const radius = Math.random() * spread.x;
            const angle = Math.random() * Math.PI * 2;

            positions[i3] = position.x + Math.cos(angle) * radius;
            positions[i3 + 1] = position.y + Math.random() * spread.y;
            positions[i3 + 2] = position.z + Math.sin(angle) * radius;

            // 沉降速度
            velocities[i3] = (Math.random() - 0.5) * 0.02;
            velocities[i3 + 1] = -settlingSpeed - Math.random() * settlingSpeed * 0.5;
            velocities[i3 + 2] = (Math.random() - 0.5) * 0.02;

            // 棕色污泥
            const particleColor = new THREE.Color(color);
            // 添加一些颜色变化
            particleColor.offsetHSL(0, 0, (Math.random() - 0.5) * 0.3);
            colors[i3] = particleColor.r;
            colors[i3 + 1] = particleColor.g;
            colors[i3 + 2] = particleColor.b;

            // 大小
            sizes[i] = size + Math.random() * size * 0.5;
        }

        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

        const material = new THREE.PointsMaterial({
            size: size,
            transparent: true,
            opacity: opacity,
            vertexColors: true
        });

        const particles = new THREE.Points(geometry, material);
        particles.userData = {
            type: 'sludge',
            config: config,
            originalPosition: position.clone(),
            isActive: true
        };

        this.scene.add(particles);
        this.particleGroups.set(name, particles);

        return particles;
    }

    // 创建化学反应效果
    createChemicalReaction(config) {
        const {
            name,
            position = new THREE.Vector3(0, 0, 0),
            count = 150,
            spread = new THREE.Vector3(2, 1, 2),
            color = 0x00ff88,
            size = 0.02,
            opacity = 0.6,
            pulseDuration = 2.0
        } = config;

        const geometry = new THREE.BufferGeometry();
        const positions = new Float32Array(count * 3);
        const colors = new Float32Array(count * 3);
        const sizes = new Float32Array(count);
        const phases = new Float32Array(count);

        for (let i = 0; i < count; i++) {
            const i3 = i * 3;

            // 随机分布
            positions[i3] = position.x + (Math.random() - 0.5) * spread.x;
            positions[i3 + 1] = position.y + (Math.random() - 0.5) * spread.y;
            positions[i3 + 2] = position.z + (Math.random() - 0.5) * spread.z;

            // 颜色
            const particleColor = new THREE.Color(color);
            colors[i3] = particleColor.r;
            colors[i3 + 1] = particleColor.g;
            colors[i3 + 2] = particleColor.b;

            // 大小和相位
            sizes[i] = size + Math.random() * size * 0.5;
            phases[i] = Math.random() * Math.PI * 2;
        }

        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
        geometry.setAttribute('phase', new THREE.BufferAttribute(phases, 1));

        const material = new THREE.PointsMaterial({
            size: size,
            transparent: true,
            opacity: opacity,
            vertexColors: true,
            blending: THREE.AdditiveBlending
        });

        const particles = new THREE.Points(geometry, material);
        particles.userData = {
            type: 'chemical',
            config: config,
            originalPosition: position.clone(),
            isActive: true,
            pulseDuration: pulseDuration
        };

        this.scene.add(particles);
        this.particleGroups.set(name, particles);

        return particles;
    }

    // 更新所有粒子系统
    update(deltaTime) {
        this.particleGroups.forEach((particles, name) => {
            if (!particles.userData.isActive) return;

            switch (particles.userData.type) {
                case 'waterFlow':
                    this.updateWaterFlow(particles, deltaTime);
                    break;
                case 'bubbles':
                    this.updateBubbles(particles, deltaTime);
                    break;
                case 'sludge':
                    this.updateSludge(particles, deltaTime);
                    break;
                case 'chemical':
                    this.updateChemical(particles, deltaTime);
                    break;
            }
        });
    }

    updateWaterFlow(particles, deltaTime) {
        const positions = particles.geometry.attributes.position.array;
        const velocities = particles.geometry.attributes.velocity.array;
        const lifetimes = particles.geometry.attributes.lifetime.array;
        const config = particles.userData.config;

        for (let i = 0; i < positions.length; i += 3) {
            const particleIndex = i / 3;

            // 更新位置
            positions[i] += velocities[i] * deltaTime;
            positions[i + 1] += velocities[i + 1] * deltaTime;
            positions[i + 2] += velocities[i + 2] * deltaTime;

            // 更新生命周期
            lifetimes[particleIndex] -= deltaTime;

            // 重置死亡的粒子
            if (lifetimes[particleIndex] <= 0) {
                const originalPos = particles.userData.originalPosition;
                positions[i] = originalPos.x + (Math.random() - 0.5) * config.spread.x;
                positions[i + 1] = originalPos.y + (Math.random() - 0.5) * config.spread.y;
                positions[i + 2] = originalPos.z + (Math.random() - 0.5) * config.spread.z;

                velocities[i] = config.velocity.x + (Math.random() - 0.5) * 0.2;
                velocities[i + 1] = config.velocity.y + (Math.random() - 0.5) * 0.1;
                velocities[i + 2] = config.velocity.z + (Math.random() - 0.5) * 0.2;

                lifetimes[particleIndex] = Math.random() * 10;
            }
        }

        particles.geometry.attributes.position.needsUpdate = true;
        particles.geometry.attributes.lifetime.needsUpdate = true;
    }

    updateBubbles(particles, deltaTime) {
        const positions = particles.geometry.attributes.position.array;
        const velocities = particles.geometry.attributes.velocity.array;
        const config = particles.userData.config;

        for (let i = 0; i < positions.length; i += 3) {
            // 更新位置
            positions[i] += velocities[i] * deltaTime;
            positions[i + 1] += velocities[i + 1] * deltaTime;
            positions[i + 2] += velocities[i + 2] * deltaTime;

            // 重置到达顶部的气泡
            if (positions[i + 1] > config.position.y + 5) {
                const originalPos = particles.userData.originalPosition;
                positions[i] = originalPos.x + (Math.random() - 0.5) * config.spread.x;
                positions[i + 1] = originalPos.y + Math.random() * config.spread.y;
                positions[i + 2] = originalPos.z + (Math.random() - 0.5) * config.spread.z;

                velocities[i] = (Math.random() - 0.5) * 0.1;
                velocities[i + 1] = config.riseSpeed + Math.random() * config.riseSpeed * 0.5;
                velocities[i + 2] = (Math.random() - 0.5) * 0.1;
            }
        }

        particles.geometry.attributes.position.needsUpdate = true;
    }

    updateSludge(particles, deltaTime) {
        const positions = particles.geometry.attributes.position.array;
        const velocities = particles.geometry.attributes.velocity.array;
        const config = particles.userData.config;

        for (let i = 0; i < positions.length; i += 3) {
            // 更新位置
            positions[i] += velocities[i] * deltaTime;
            positions[i + 1] += velocities[i + 1] * deltaTime;
            positions[i + 2] += velocities[i + 2] * deltaTime;

            // 到达底部时停止或重置
            if (positions[i + 1] <= config.position.y - 2) {
                if (Math.random() < 0.01) { // 1%概率重新悬浮
                    const originalPos = particles.userData.originalPosition;
                    const radius = Math.random() * config.spread.x;
                    const angle = Math.random() * Math.PI * 2;

                    positions[i] = originalPos.x + Math.cos(angle) * radius;
                    positions[i + 1] = originalPos.y + Math.random() * config.spread.y;
                    positions[i + 2] = originalPos.z + Math.sin(angle) * radius;

                    velocities[i] = (Math.random() - 0.5) * 0.02;
                    velocities[i + 1] = -config.settlingSpeed - Math.random() * config.settlingSpeed * 0.5;
                    velocities[i + 2] = (Math.random() - 0.5) * 0.02;
                } else {
                    velocities[i + 1] = 0; // 停在底部
                }
            }
        }

        particles.geometry.attributes.position.needsUpdate = true;
    }

    updateChemical(particles, deltaTime) {
        const time = Date.now() * 0.001;
        const colors = particles.geometry.attributes.color.array;
        const phases = particles.geometry.attributes.phase.array;
        const config = particles.userData.config;

        for (let i = 0; i < colors.length; i += 3) {
            const particleIndex = i / 3;
            const phase = phases[particleIndex];
            const intensity = 0.5 + 0.5 * Math.sin(time * (2 * Math.PI / config.pulseDuration) + phase);

            // 更新颜色强度
            const baseColor = new THREE.Color(config.color);
            colors[i] = baseColor.r * intensity;
            colors[i + 1] = baseColor.g * intensity;
            colors[i + 2] = baseColor.b * intensity;
        }

        particles.geometry.attributes.color.needsUpdate = true;
    }

    // 控制方法
    setVisible(visible) {
        this.isVisible = visible;
        this.particleGroups.forEach(particles => {
            particles.visible = visible;
        });
    }

    setGlobalIntensity(intensity) {
        this.globalIntensity = intensity;
        this.particleGroups.forEach(particles => {
            particles.material.opacity = particles.userData.config.opacity * intensity;
        });
    }

    startAllEffects() {
        this.particleGroups.forEach(particles => {
            particles.userData.isActive = true;
        });
    }

    stopAllEffects() {
        this.particleGroups.forEach(particles => {
            particles.userData.isActive = false;
        });
    }

    startEffect(name) {
        const particles = this.particleGroups.get(name);
        if (particles) {
            particles.userData.isActive = true;
        }
    }

    stopEffect(name) {
        const particles = this.particleGroups.get(name);
        if (particles) {
            particles.userData.isActive = false;
        }
    }

    removeEffect(name) {
        const particles = this.particleGroups.get(name);
        if (particles) {
            this.scene.remove(particles);
            particles.geometry.dispose();
            particles.material.dispose();
            this.particleGroups.delete(name);
        }
    }

    getParticleCount() {
        let total = 0;
        this.particleGroups.forEach(particles => {
            total += particles.geometry.attributes.position.count;
        });
        return total;
    }

    dispose() {
        this.particleGroups.forEach((particles, name) => {
            this.scene.remove(particles);
            particles.geometry.dispose();
            particles.material.dispose();
        });
        this.particleGroups.clear();
    }
}
