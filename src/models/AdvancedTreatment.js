/**
 * 深度处理单元 - 包含过滤、消毒、脱氮除磷等高级处理工艺
 */

import * as THREE from 'three';
import { Equipment } from './Equipment.js';

export class AdvancedTreatment extends Equipment {
    constructor(position) {
        super(position);
        
        // 深度处理参数
        this.parameters.set('filterBackwashCycle', 24); // 反冲洗周期 (小时)
        this.parameters.set('uvIntensity', 30); // UV强度 (mJ/cm²)
        this.parameters.set('chlorineDosage', 2.5); // 氯投加量 (mg/L)
        this.parameters.set('phosphorusRemoval', 0.5); // 除磷效率
        this.parameters.set('nitrogenRemoval', 0.8); // 脱氮效率
        
        // 处理单元
        this.filterTank = null;
        this.uvReactor = null;
        this.disinfectionTank = null;
        
        // 特效
        this.uvLights = [];
        this.filterParticles = null;
        this.disinfectionParticles = null;
    }

    async create() {
        this.createBasicMaterials();
        this.createBasicGeometries();
        
        // 创建过滤单元
        this.createFilterUnit();
        
        // 创建UV消毒单元
        this.createUVDisinfectionUnit();
        
        // 创建化学消毒单元
        this.createChemicalDisinfectionUnit();
        
        // 创建脱氮除磷单元
        this.createNutrientRemovalUnit();
        
        // 创建控制系统
        this.createControlSystem();
        
        // 创建处理效果
        this.createTreatmentEffects();
        
        // 添加指示灯和标识
        this.createIndicatorLight(0x00ff00, new THREE.Vector3(0, 6, 0));
        this.createNamePlate('深度处理', new THREE.Vector3(0, 6.5, 0));
        
        // 设置动画
        this.setupAnimations();
        
        this.updateBoundingBox();
    }

    createFilterUnit() {
        const filterGroup = new THREE.Group();
        
        // 过滤池主体
        const filterGeometry = new THREE.BoxGeometry(4, 3, 3);
        const filterMaterial = this.materials.get('concrete');
        
        const filterTank = new THREE.Mesh(filterGeometry, filterMaterial);
        filterTank.position.set(-6, 1.5, -3);
        filterTank.castShadow = true;
        filterGroup.add(filterTank);
        
        // 滤料层
        const mediaLayers = [
            { height: 0.3, color: 0x8b4513, name: '无烟煤' },
            { height: 0.4, color: 0xdaa520, name: '石英砂' },
            { height: 0.2, color: 0x696969, name: '砾石' }
        ];
        
        let currentHeight = 0.2;
        mediaLayers.forEach((layer, index) => {
            const layerGeometry = new THREE.BoxGeometry(3.8, layer.height, 2.8);
            const layerMaterial = new THREE.MeshPhongMaterial({
                color: layer.color,
                transparent: true,
                opacity: 0.8
            });
            
            const layerMesh = new THREE.Mesh(layerGeometry, layerMaterial);
            layerMesh.position.set(-6, currentHeight + layer.height / 2, -3);
            filterGroup.add(layerMesh);
            
            currentHeight += layer.height;
        });
        
        // 进水分配器
        const distributorGeometry = new THREE.CylinderGeometry(0.1, 0.1, 3.5);
        const distributorMaterial = this.materials.get('metal');
        
        for (let i = 0; i < 5; i++) {
            const distributor = new THREE.Mesh(distributorGeometry, distributorMaterial);
            distributor.position.set(-6 + (i - 2) * 0.8, 2.8, -3);
            distributor.rotation.z = Math.PI / 2;
            filterGroup.add(distributor);
        }
        
        // 反冲洗系统
        const backwashPipeGeometry = new THREE.CylinderGeometry(0.2, 0.2, 4);
        const backwashPipe = new THREE.Mesh(backwashPipeGeometry, distributorMaterial);
        backwashPipe.position.set(-6, -0.5, -3);
        backwashPipe.rotation.z = Math.PI / 2;
        filterGroup.add(backwashPipe);
        
        // 反冲洗泵
        const backwashPumpGeometry = new THREE.CylinderGeometry(0.5, 0.5, 1);
        const backwashPumpMaterial = new THREE.MeshPhongMaterial({
            color: 0x2980b9
        });
        
        const backwashPump = new THREE.Mesh(backwashPumpGeometry, backwashPumpMaterial);
        backwashPump.position.set(-8, -0.5, -3);
        filterGroup.add(backwashPump);
        
        this.group.add(filterGroup);
        this.filterTank = filterGroup;
    }

    createUVDisinfectionUnit() {
        const uvGroup = new THREE.Group();
        
        // UV反应器主体
        const reactorGeometry = new THREE.CylinderGeometry(1, 1, 4);
        const reactorMaterial = new THREE.MeshPhongMaterial({
            color: 0x2c3e50,
            metalness: 0.8,
            roughness: 0.2
        });
        
        const reactor = new THREE.Mesh(reactorGeometry, reactorMaterial);
        reactor.position.set(-2, 2, -3);
        reactor.rotation.z = Math.PI / 2;
        reactor.castShadow = true;
        uvGroup.add(reactor);
        
        // UV灯管
        const lampCount = 8;
        const lampGeometry = new THREE.CylinderGeometry(0.05, 0.05, 3.5);
        const lampMaterial = new THREE.MeshPhongMaterial({
            color: 0xffffff,
            emissive: 0x4444ff,
            emissiveIntensity: 0.5
        });
        
        for (let i = 0; i < lampCount; i++) {
            const angle = (i * 2 * Math.PI) / lampCount;
            const x = Math.cos(angle) * 0.6;
            const z = Math.sin(angle) * 0.6;
            
            const lamp = new THREE.Mesh(lampGeometry, lampMaterial);
            lamp.position.set(-2 + z, 2 + x, -3);
            lamp.rotation.z = Math.PI / 2;
            uvGroup.add(lamp);
            
            this.uvLights.push(lamp);
            
            // UV光效
            const uvLight = new THREE.PointLight(0x8888ff, 0.5, 5);
            uvLight.position.set(-2 + z, 2 + x, -3);
            uvGroup.add(uvLight);
        }
        
        // 石英套管
        const sleeveGeometry = new THREE.CylinderGeometry(0.08, 0.08, 3.5);
        const sleeveMaterial = new THREE.MeshPhongMaterial({
            color: 0xffffff,
            transparent: true,
            opacity: 0.3
        });
        
        for (let i = 0; i < lampCount; i++) {
            const angle = (i * 2 * Math.PI) / lampCount;
            const x = Math.cos(angle) * 0.6;
            const z = Math.sin(angle) * 0.6;
            
            const sleeve = new THREE.Mesh(sleeveGeometry, sleeveMaterial);
            sleeve.position.set(-2 + z, 2 + x, -3);
            sleeve.rotation.z = Math.PI / 2;
            uvGroup.add(sleeve);
        }
        
        // 控制柜
        const controlBoxGeometry = new THREE.BoxGeometry(1, 1.5, 0.5);
        const controlBoxMaterial = new THREE.MeshPhongMaterial({
            color: 0x34495e
        });
        
        const controlBox = new THREE.Mesh(controlBoxGeometry, controlBoxMaterial);
        controlBox.position.set(-2, 4, -5);
        uvGroup.add(controlBox);
        
        this.group.add(uvGroup);
        this.uvReactor = uvGroup;
    }

    createChemicalDisinfectionUnit() {
        const disinfectionGroup = new THREE.Group();
        
        // 接触池
        const contactTankGeometry = new THREE.BoxGeometry(3, 2, 4);
        const contactTankMaterial = this.materials.get('concrete');
        
        const contactTank = new THREE.Mesh(contactTankGeometry, contactTankMaterial);
        contactTank.position.set(2, 1, -2);
        contactTank.castShadow = true;
        disinfectionGroup.add(contactTank);
        
        // 水面
        const waterGeometry = new THREE.PlaneGeometry(2.8, 3.8);
        const waterMaterial = this.materials.get('water');
        
        const water = new THREE.Mesh(waterGeometry, waterMaterial);
        water.position.set(2, 1.9, -2);
        water.rotation.x = -Math.PI / 2;
        disinfectionGroup.add(water);
        
        // 加氯设备
        const chlorinatorGeometry = new THREE.CylinderGeometry(0.4, 0.4, 1.5);
        const chlorinatorMaterial = new THREE.MeshPhongMaterial({
            color: 0xf39c12
        });
        
        const chlorinator = new THREE.Mesh(chlorinatorGeometry, chlorinatorMaterial);
        chlorinator.position.set(4, 1.75, -2);
        disinfectionGroup.add(chlorinator);
        
        // 投药管道
        const dosePipeGeometry = new THREE.CylinderGeometry(0.05, 0.05, 1);
        const dosePipeMaterial = this.materials.get('metal');
        
        const dosePipe = new THREE.Mesh(dosePipeGeometry, dosePipeMaterial);
        dosePipe.position.set(3.5, 1.5, -2);
        dosePipe.rotation.z = Math.PI / 4;
        disinfectionGroup.add(dosePipe);
        
        // 搅拌器
        const mixerGeometry = new THREE.CylinderGeometry(0.1, 0.1, 1.5);
        const mixerMaterial = this.materials.get('metal');
        
        const mixer = new THREE.Mesh(mixerGeometry, mixerMaterial);
        mixer.position.set(2, 1.25, -2);
        disinfectionGroup.add(mixer);
        
        // 搅拌叶片
        const bladeGeometry = new THREE.BoxGeometry(0.8, 0.05, 0.2);
        const bladeMaterial = new THREE.MeshPhongMaterial({
            color: 0x34495e
        });
        
        const blade = new THREE.Mesh(bladeGeometry, bladeMaterial);
        blade.position.set(2, 0.5, -2);
        disinfectionGroup.add(blade);
        
        this.group.add(disinfectionGroup);
        this.disinfectionTank = disinfectionGroup;
    }

    createNutrientRemovalUnit() {
        const nutrientGroup = new THREE.Group();
        
        // 除磷反应器
        const phosphorusReactorGeometry = new THREE.CylinderGeometry(1.2, 1.2, 2.5);
        const reactorMaterial = this.materials.get('metal');
        
        const phosphorusReactor = new THREE.Mesh(phosphorusReactorGeometry, reactorMaterial);
        phosphorusReactor.position.set(6, 1.25, -4);
        phosphorusReactor.castShadow = true;
        nutrientGroup.add(phosphorusReactor);
        
        // 脱氮反应器
        const nitrogenReactorGeometry = new THREE.CylinderGeometry(1.2, 1.2, 2.5);
        const nitrogenReactor = new THREE.Mesh(nitrogenReactorGeometry, reactorMaterial);
        nitrogenReactor.position.set(6, 1.25, -1);
        nitrogenReactor.castShadow = true;
        nutrientGroup.add(nitrogenReactor);
        
        // 药剂投加系统
        const dosageSystemGeometry = new THREE.BoxGeometry(1.5, 2, 1);
        const dosageSystemMaterial = new THREE.MeshPhongMaterial({
            color: 0x27ae60
        });
        
        const dosageSystem = new THREE.Mesh(dosageSystemGeometry, dosageSystemMaterial);
        dosageSystem.position.set(8, 1, -2.5);
        nutrientGroup.add(dosageSystem);
        
        // 连接管道
        const pipeGeometry = new THREE.CylinderGeometry(0.1, 0.1, 2);
        const pipeMaterial = this.materials.get('metal');
        
        const pipe1 = new THREE.Mesh(pipeGeometry, pipeMaterial);
        pipe1.position.set(7, 1.5, -3.5);
        pipe1.rotation.z = Math.PI / 4;
        nutrientGroup.add(pipe1);
        
        const pipe2 = new THREE.Mesh(pipeGeometry, pipeMaterial);
        pipe2.position.set(7, 1.5, -1.5);
        pipe2.rotation.z = -Math.PI / 4;
        nutrientGroup.add(pipe2);
        
        this.group.add(nutrientGroup);
        this.nutrientRemovalUnit = nutrientGroup;
    }

    createControlSystem() {
        // 中央控制柜
        const controlPanelGeometry = new THREE.BoxGeometry(2, 2.5, 0.8);
        const controlPanelMaterial = new THREE.MeshPhongMaterial({
            color: 0x34495e
        });
        
        const controlPanel = this.addMesh(
            controlPanelGeometry,
            controlPanelMaterial,
            new THREE.Vector3(0, 2.25, 2)
        );
        
        // 显示屏
        const screenGeometry = new THREE.PlaneGeometry(1.2, 0.8);
        const screenMaterial = new THREE.MeshPhongMaterial({
            color: 0x000000,
            emissive: 0x001100
        });
        
        const screen = new THREE.Mesh(screenGeometry, screenMaterial);
        screen.position.set(0, 2.8, 2.4);
        this.group.add(screen);
        
        // 状态指示灯
        const indicatorColors = [0xe74c3c, 0xf39c12, 0x27ae60, 0x3498db];
        const indicatorLabels = ['过滤', 'UV', '消毒', '脱氮除磷'];
        
        indicatorColors.forEach((color, index) => {
            const indicatorGeometry = new THREE.SphereGeometry(0.05);
            const indicatorMaterial = new THREE.MeshPhongMaterial({
                color: color,
                emissive: color,
                emissiveIntensity: 0.3
            });
            
            const indicator = new THREE.Mesh(indicatorGeometry, indicatorMaterial);
            indicator.position.set(-0.6 + index * 0.4, 1.8, 2.4);
            this.group.add(indicator);
        });
        
        this.controlSystem = {
            panel: controlPanel,
            screen: screen
        };
    }

    createTreatmentEffects() {
        // 过滤效果粒子
        this.createFilterParticles();
        
        // UV消毒效果
        this.createUVEffect();
        
        // 化学消毒效果
        this.createDisinfectionParticles();
    }

    createFilterParticles() {
        const particleCount = 300;
        const geometry = new THREE.BufferGeometry();
        
        const positions = new Float32Array(particleCount * 3);
        const velocities = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        
        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;
            
            // 在过滤池中分布
            positions[i3] = -6 + (Math.random() - 0.5) * 3.5;
            positions[i3 + 1] = 0.5 + Math.random() * 2.5;
            positions[i3 + 2] = -3 + (Math.random() - 0.5) * 2.5;
            
            // 向下过滤
            velocities[i3] = (Math.random() - 0.5) * 0.02;
            velocities[i3 + 1] = -0.1 - Math.random() * 0.1;
            velocities[i3 + 2] = (Math.random() - 0.5) * 0.02;
            
            // 颜色变化（从浑浊到清澈）
            const clarity = Math.max(0, (3 - positions[i3 + 1]) / 3);
            const color = new THREE.Color();
            color.setHSL(0.6, 0.5, 0.3 + clarity * 0.4);
            colors[i3] = color.r;
            colors[i3 + 1] = color.g;
            colors[i3 + 2] = color.b;
        }
        
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        
        const material = new THREE.PointsMaterial({
            size: 0.03,
            transparent: true,
            opacity: 0.7,
            vertexColors: true
        });
        
        this.filterParticles = new THREE.Points(geometry, material);
        this.group.add(this.filterParticles);
    }

    createUVEffect() {
        // UV光束效果
        const beamCount = 8;
        for (let i = 0; i < beamCount; i++) {
            const angle = (i * 2 * Math.PI) / beamCount;
            const x = Math.cos(angle) * 0.6;
            const z = Math.sin(angle) * 0.6;
            
            const beamGeometry = new THREE.CylinderGeometry(0.02, 0.02, 3.5);
            const beamMaterial = new THREE.MeshBasicMaterial({
                color: 0x8888ff,
                transparent: true,
                opacity: 0.3
            });
            
            const beam = new THREE.Mesh(beamGeometry, beamMaterial);
            beam.position.set(-2 + z, 2 + x, -3);
            beam.rotation.z = Math.PI / 2;
            this.group.add(beam);
        }
    }

    createDisinfectionParticles() {
        const particleCount = 200;
        const geometry = new THREE.BufferGeometry();
        
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        
        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;
            
            // 在接触池中分布
            positions[i3] = 2 + (Math.random() - 0.5) * 2.5;
            positions[i3 + 1] = 0.5 + Math.random() * 1.5;
            positions[i3 + 2] = -2 + (Math.random() - 0.5) * 3.5;
            
            // 氯气颜色效果
            const color = new THREE.Color(0x00ff88);
            colors[i3] = color.r;
            colors[i3 + 1] = color.g;
            colors[i3 + 2] = color.b;
        }
        
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        
        const material = new THREE.PointsMaterial({
            size: 0.02,
            transparent: true,
            opacity: 0.6,
            vertexColors: true,
            blending: THREE.AdditiveBlending
        });
        
        this.disinfectionParticles = new THREE.Points(geometry, material);
        this.group.add(this.disinfectionParticles);
    }

    setupAnimations() {
        // 过滤动画
        this.addAnimation('filtering', (start) => {
            this.filteringActive = start;
        });
        
        // UV消毒动画
        this.addAnimation('uvDisinfection', (start) => {
            this.uvActive = start;
        });
        
        // 化学消毒动画
        this.addAnimation('chemicalDisinfection', (start) => {
            this.disinfectionActive = start;
        });
    }

    updateEquipment(deltaTime) {
        // 更新过滤粒子
        if (this.filteringActive && this.filterParticles) {
            this.updateFilterParticles(deltaTime);
        }
        
        // 更新UV灯效果
        if (this.uvActive) {
            this.updateUVLights(deltaTime);
        }
        
        // 更新消毒粒子
        if (this.disinfectionActive && this.disinfectionParticles) {
            this.updateDisinfectionParticles(deltaTime);
        }
    }

    updateFilterParticles(deltaTime) {
        const positions = this.filterParticles.geometry.attributes.position.array;
        const velocities = this.filterParticles.geometry.attributes.velocity.array;
        const colors = this.filterParticles.geometry.attributes.color.array;
        
        for (let i = 0; i < positions.length; i += 3) {
            positions[i] += velocities[i] * deltaTime;
            positions[i + 1] += velocities[i + 1] * deltaTime;
            positions[i + 2] += velocities[i + 2] * deltaTime;
            
            // 重置到达底部的粒子
            if (positions[i + 1] < 0.2) {
                positions[i] = -6 + (Math.random() - 0.5) * 3.5;
                positions[i + 1] = 3;
                positions[i + 2] = -3 + (Math.random() - 0.5) * 2.5;
                
                velocities[i] = (Math.random() - 0.5) * 0.02;
                velocities[i + 1] = -0.1 - Math.random() * 0.1;
                velocities[i + 2] = (Math.random() - 0.5) * 0.02;
            }
            
            // 更新颜色（过滤效果）
            const clarity = Math.max(0, (3 - positions[i + 1]) / 3);
            const color = new THREE.Color();
            color.setHSL(0.6, 0.5, 0.3 + clarity * 0.4);
            colors[i] = color.r;
            colors[i + 1] = color.g;
            colors[i + 2] = color.b;
        }
        
        this.filterParticles.geometry.attributes.position.needsUpdate = true;
        this.filterParticles.geometry.attributes.color.needsUpdate = true;
    }

    updateUVLights(deltaTime) {
        const time = Date.now() * 0.001;
        
        this.uvLights.forEach((light, index) => {
            const intensity = 0.3 + 0.2 * Math.sin(time * 3 + index);
            light.material.emissiveIntensity = intensity;
        });
    }

    updateDisinfectionParticles(deltaTime) {
        const time = Date.now() * 0.001;
        const colors = this.disinfectionParticles.geometry.attributes.color.array;
        
        for (let i = 0; i < colors.length; i += 3) {
            const intensity = 0.5 + 0.5 * Math.sin(time * 2 + i * 0.1);
            colors[i] = 0 * intensity;
            colors[i + 1] = 1 * intensity;
            colors[i + 2] = 0.5 * intensity;
        }
        
        this.disinfectionParticles.geometry.attributes.color.needsUpdate = true;
    }

    getStatus() {
        const baseStatus = super.getStatus();
        return {
            ...baseStatus,
            filterBackwashCycle: this.parameters.get('filterBackwashCycle'),
            uvIntensity: this.parameters.get('uvIntensity'),
            chlorineDosage: this.parameters.get('chlorineDosage'),
            phosphorusRemoval: this.parameters.get('phosphorusRemoval'),
            nitrogenRemoval: this.parameters.get('nitrogenRemoval'),
            overallEfficiency: this.getOverallEfficiency(),
            turbidityRemoval: 95 + Math.random() * 4, // 模拟浊度去除率
            pathogenRemoval: 99.9 + Math.random() * 0.09, // 模拟病原体去除率
            totalNitrogen: 5 + Math.random() * 3, // 模拟总氮
            totalPhosphorus: 0.3 + Math.random() * 0.2 // 模拟总磷
        };
    }

    getOverallEfficiency() {
        const filterEfficiency = 0.95;
        const uvEfficiency = 0.999;
        const disinfectionEfficiency = 0.99;
        const nutrientEfficiency = (this.parameters.get('phosphorusRemoval') + this.parameters.get('nitrogenRemoval')) / 2;
        
        return filterEfficiency * uvEfficiency * disinfectionEfficiency * nutrientEfficiency * this.efficiency;
    }
}
