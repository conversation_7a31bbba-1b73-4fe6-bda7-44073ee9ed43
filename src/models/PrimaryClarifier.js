/**
 * 初沉池 - 初级沉淀池，用于去除可沉淀的悬浮物和部分有机物
 */

import * as THREE from 'three';
import { Equipment } from './Equipment.js';

export class PrimaryClarifier extends Equipment {
    constructor(position) {
        super(position);
        
        // 初沉池特有参数
        this.parameters.set('retentionTime', 1.5); // 停留时间 (小时)
        this.parameters.set('surfaceLoading', 30); // 表面负荷 (m³/m²·d)
        this.parameters.set('waterLevel', 2.5); // 水位 (米)
        this.parameters.set('sludgeLevel', 0.5); // 污泥层厚度 (米)
        this.parameters.set('scraperSpeed', 0.5); // 刮泥器转速 (rpm)
        
        // 刮泥系统
        this.scraper = null;
        this.scraperRotation = 0;
        
        // 粒子效果
        this.sedimentParticles = null;
        this.waterFlow = null;
        this.sludgeParticles = null;
    }

    async create() {
        this.createBasicMaterials();
        this.createBasicGeometries();
        
        // 创建主体结构
        this.createMainStructure();
        
        // 创建刮泥系统
        this.createScrapingSystem();
        
        // 创建进出水系统
        this.createInletOutletSystem();
        
        // 创建污泥排放系统
        this.createSludgeSystem();
        
        // 创建沉淀效果
        this.createSedimentationEffects();
        
        // 添加指示灯和标识
        this.createIndicatorLight(0x00ff00, new THREE.Vector3(0, 4, 0));
        this.createNamePlate('初沉池', new THREE.Vector3(0, 4.5, 0));
        
        // 设置动画
        this.setupAnimations();
        
        this.updateBoundingBox();
    }

    createMainStructure() {
        // 圆形池体
        const poolGeometry = new THREE.CylinderGeometry(5, 5, 3);
        const poolMaterial = this.materials.get('concrete');
        
        const pool = this.addMesh(
            poolGeometry,
            poolMaterial,
            new THREE.Vector3(0, 1.5, 0)
        );
        pool.userData.isMainStructure = true;
        
        // 内壁
        const innerGeometry = new THREE.CylinderGeometry(4.8, 4.8, 2.8);
        const innerMaterial = new THREE.MeshPhongMaterial({
            color: 0x95a5a6,
            side: THREE.BackSide
        });
        
        const innerWall = this.addMesh(
            innerGeometry,
            innerMaterial,
            new THREE.Vector3(0, 1.5, 0)
        );
        
        // 水面
        const waterGeometry = new THREE.CircleGeometry(4.7, 32);
        const waterMaterial = this.materials.get('water');
        
        const water = this.addMesh(
            waterGeometry,
            waterMaterial,
            new THREE.Vector3(0, this.parameters.get('waterLevel'), 0),
            new THREE.Euler(-Math.PI / 2, 0, 0)
        );
        water.userData.isWater = true;
        
        this.waterSurface = water;
        
        // 底部锥形结构
        const bottomConeGeometry = new THREE.ConeGeometry(2, 1, 16);
        const bottomCone = this.addMesh(
            bottomConeGeometry,
            poolMaterial,
            new THREE.Vector3(0, 0.5, 0)
        );
        
        // 中心进水井
        const centerWellGeometry = new THREE.CylinderGeometry(0.8, 0.8, 2);
        const centerWell = this.addMesh(
            centerWellGeometry,
            poolMaterial,
            new THREE.Vector3(0, 1.5, 0)
        );
        centerWell.userData.isCenterWell = true;
        
        // 挡板
        const baffleGeometry = new THREE.CylinderGeometry(1.2, 1.2, 0.5);
        const baffleMaterial = new THREE.MeshPhongMaterial({
            color: 0x7f8c8d
        });
        
        const baffle = this.addMesh(
            baffleGeometry,
            baffleMaterial,
            new THREE.Vector3(0, 2.2, 0)
        );
        baffle.userData.isBaffle = true;
    }

    createScrapingSystem() {
        const scraperGroup = new THREE.Group();
        
        // 中心轴
        const shaftGeometry = new THREE.CylinderGeometry(0.15, 0.15, 4);
        const shaftMaterial = this.materials.get('metal');
        
        const shaft = new THREE.Mesh(shaftGeometry, shaftMaterial);
        shaft.position.set(0, 2, 0);
        shaft.castShadow = true;
        scraperGroup.add(shaft);
        
        // 刮泥臂
        const armGeometry = new THREE.BoxGeometry(4.5, 0.15, 0.3);
        const armMaterial = new THREE.MeshPhongMaterial({
            color: 0x2c3e50
        });
        
        const arm = new THREE.Mesh(armGeometry, armMaterial);
        arm.position.set(0, 0.3, 0);
        arm.castShadow = true;
        scraperGroup.add(arm);
        
        // 刮板
        const bladeGeometry = new THREE.BoxGeometry(0.1, 0.4, 4.5);
        const bladeMaterial = new THREE.MeshPhongMaterial({
            color: 0x34495e
        });
        
        const blade = new THREE.Mesh(bladeGeometry, bladeMaterial);
        blade.position.set(4.2, 0.2, 0);
        blade.castShadow = true;
        scraperGroup.add(blade);
        
        // 浮渣刮板
        const scumBladeGeometry = new THREE.BoxGeometry(0.1, 0.2, 4.5);
        const scumBlade = new THREE.Mesh(scumBladeGeometry, bladeMaterial);
        scumBlade.position.set(4.2, 2.4, 0);
        scraperGroup.add(scumBlade);
        
        // 驱动装置
        const driveGeometry = new THREE.CylinderGeometry(0.5, 0.5, 1);
        const driveMaterial = new THREE.MeshPhongMaterial({
            color: 0x3498db
        });
        
        const drive = new THREE.Mesh(driveGeometry, driveMaterial);
        drive.position.set(0, 4.5, 0);
        drive.castShadow = true;
        scraperGroup.add(drive);
        
        // 支撑桥
        const bridgeGeometry = new THREE.BoxGeometry(10, 0.3, 0.5);
        const bridgeMaterial = this.materials.get('metal');
        
        const bridge = new THREE.Mesh(bridgeGeometry, bridgeMaterial);
        bridge.position.set(0, 4, 0);
        bridge.castShadow = true;
        scraperGroup.add(bridge);
        
        this.group.add(scraperGroup);
        this.scraper = scraperGroup;
    }

    createInletOutletSystem() {
        // 进水管
        const inletPipeGeometry = new THREE.CylinderGeometry(0.4, 0.4, 2);
        const pipeMaterial = this.materials.get('metal');
        
        const inletPipe = this.addMesh(
            inletPipeGeometry,
            pipeMaterial,
            new THREE.Vector3(0, 0.5, 0)
        );
        inletPipe.userData.isInletPipe = true;
        
        // 出水堰
        const weirCount = 16;
        const weirGeometry = new THREE.BoxGeometry(0.6, 0.3, 0.1);
        const weirMaterial = new THREE.MeshPhongMaterial({
            color: 0x7f8c8d
        });
        
        for (let i = 0; i < weirCount; i++) {
            const angle = (i * 2 * Math.PI) / weirCount;
            const x = Math.cos(angle) * 4.5;
            const z = Math.sin(angle) * 4.5;
            
            const weir = new THREE.Mesh(weirGeometry, weirMaterial);
            weir.position.set(x, 2.4, z);
            weir.rotation.y = angle;
            weir.castShadow = true;
            this.group.add(weir);
        }
        
        // 出水槽
        const troughGeometry = new THREE.TorusGeometry(4.8, 0.2, 8, 32);
        const trough = this.addMesh(
            troughGeometry,
            pipeMaterial,
            new THREE.Vector3(0, 2.3, 0)
        );
        trough.userData.isOutletTrough = true;
        
        // 出水管
        const outletPipe = this.addMesh(
            inletPipeGeometry,
            pipeMaterial,
            new THREE.Vector3(5.5, 1.5, 0),
            new THREE.Euler(0, 0, Math.PI / 2)
        );
        outletPipe.userData.isOutletPipe = true;
    }

    createSludgeSystem() {
        // 污泥排放管
        const sludgePipeGeometry = new THREE.CylinderGeometry(0.3, 0.3, 1.5);
        const pipeMaterial = this.materials.get('metal');
        
        const sludgePipe = this.addMesh(
            sludgePipeGeometry,
            pipeMaterial,
            new THREE.Vector3(0, -0.5, 3),
            new THREE.Euler(Math.PI / 4, 0, 0)
        );
        sludgePipe.userData.isSludgePipe = true;
        
        // 污泥阀
        const valveGeometry = new THREE.SphereGeometry(0.4);
        const valveMaterial = new THREE.MeshPhongMaterial({
            color: 0xe74c3c
        });
        
        const sludgeValve = this.addMesh(
            valveGeometry,
            valveMaterial,
            new THREE.Vector3(0, -0.2, 3.5)
        );
        sludgeValve.userData.isSludgeValve = true;
        
        // 污泥泵
        const pumpGeometry = new THREE.CylinderGeometry(0.6, 0.6, 1);
        const pumpMaterial = new THREE.MeshPhongMaterial({
            color: 0x2980b9
        });
        
        const sludgePump = this.addMesh(
            pumpGeometry,
            pumpMaterial,
            new THREE.Vector3(0, -1, 4)
        );
        sludgePump.userData.isSludgePump = true;
        
        this.sludgeSystem = {
            pipe: sludgePipe,
            valve: sludgeValve,
            pump: sludgePump
        };
    }

    createSedimentationEffects() {
        // 悬浮物沉降粒子
        const particleCount = 600;
        const geometry = new THREE.BufferGeometry();
        
        const positions = new Float32Array(particleCount * 3);
        const velocities = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        const sizes = new Float32Array(particleCount);
        
        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;
            
            // 随机分布在池中
            const radius = Math.random() * 4.5;
            const angle = Math.random() * Math.PI * 2;
            
            positions[i3] = Math.cos(angle) * radius;
            positions[i3 + 1] = 0.5 + Math.random() * 2;
            positions[i3 + 2] = Math.sin(angle) * radius;
            
            // 沉降速度
            velocities[i3] = (Math.random() - 0.5) * 0.02;
            velocities[i3 + 1] = -0.1 - Math.random() * 0.1;
            velocities[i3 + 2] = (Math.random() - 0.5) * 0.02;
            
            // 颜色（棕色悬浮物）
            const color = new THREE.Color();
            color.setHSL(0.08 + Math.random() * 0.1, 0.7, 0.3 + Math.random() * 0.3);
            colors[i3] = color.r;
            colors[i3 + 1] = color.g;
            colors[i3 + 2] = color.b;
            
            // 大小
            sizes[i] = 0.02 + Math.random() * 0.03;
        }
        
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
        
        const material = new THREE.PointsMaterial({
            size: 0.04,
            transparent: true,
            opacity: 0.8,
            vertexColors: true
        });
        
        this.sedimentParticles = new THREE.Points(geometry, material);
        this.group.add(this.sedimentParticles);
        
        // 水流粒子
        this.createWaterFlowParticles();
        
        // 污泥层粒子
        this.createSludgeLayerParticles();
    }

    createWaterFlowParticles() {
        const flowCount = 300;
        const geometry = new THREE.BufferGeometry();
        
        const positions = new Float32Array(flowCount * 3);
        const velocities = new Float32Array(flowCount * 3);
        const colors = new Float32Array(flowCount * 3);
        
        for (let i = 0; i < flowCount; i++) {
            const i3 = i * 3;
            
            // 从中心进水井开始
            const radius = Math.random() * 0.7;
            const angle = Math.random() * Math.PI * 2;
            
            positions[i3] = Math.cos(angle) * radius;
            positions[i3 + 1] = 1.5 + Math.random() * 0.5;
            positions[i3 + 2] = Math.sin(angle) * radius;
            
            // 径向流动
            const flowAngle = Math.random() * Math.PI * 2;
            velocities[i3] = Math.cos(flowAngle) * 0.2;
            velocities[i3 + 1] = -0.02; // 轻微下沉
            velocities[i3 + 2] = Math.sin(flowAngle) * 0.2;
            
            // 蓝色水流
            const color = new THREE.Color(0x006994);
            colors[i3] = color.r;
            colors[i3 + 1] = color.g;
            colors[i3 + 2] = color.b;
        }
        
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        
        const material = new THREE.PointsMaterial({
            size: 0.05,
            transparent: true,
            opacity: 0.6,
            vertexColors: true,
            blending: THREE.AdditiveBlending
        });
        
        this.waterFlow = new THREE.Points(geometry, material);
        this.group.add(this.waterFlow);
    }

    createSludgeLayerParticles() {
        const sludgeCount = 400;
        const geometry = new THREE.BufferGeometry();
        
        const positions = new Float32Array(sludgeCount * 3);
        const colors = new Float32Array(sludgeCount * 3);
        
        for (let i = 0; i < sludgeCount; i++) {
            const i3 = i * 3;
            
            // 分布在池底
            const radius = Math.random() * 4.5;
            const angle = Math.random() * Math.PI * 2;
            
            positions[i3] = Math.cos(angle) * radius;
            positions[i3 + 1] = 0.1 + Math.random() * this.parameters.get('sludgeLevel');
            positions[i3 + 2] = Math.sin(angle) * radius;
            
            // 深棕色污泥
            const color = new THREE.Color(0x3e2723);
            colors[i3] = color.r;
            colors[i3 + 1] = color.g;
            colors[i3 + 2] = color.b;
        }
        
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        
        const material = new THREE.PointsMaterial({
            size: 0.06,
            transparent: true,
            opacity: 0.9,
            vertexColors: true
        });
        
        this.sludgeParticles = new THREE.Points(geometry, material);
        this.group.add(this.sludgeParticles);
    }

    setupAnimations() {
        // 刮泥器旋转动画
        this.addAnimation('scraping', (start) => {
            this.scrapingActive = start;
        });
        
        // 沉淀动画
        this.addAnimation('sedimentation', (start) => {
            this.sedimentationActive = start;
        });
        
        // 水流动画
        this.addAnimation('waterFlow', (start) => {
            this.waterFlowActive = start;
        });
    }

    updateEquipment(deltaTime) {
        // 更新刮泥器旋转
        if (this.scrapingActive && this.scraper) {
            const rpm = this.parameters.get('scraperSpeed');
            const rotationSpeed = (rpm / 60) * 2 * Math.PI;
            this.scraperRotation += rotationSpeed * deltaTime;
            this.scraper.rotation.y = this.scraperRotation;
        }
        
        // 更新沉淀粒子
        if (this.sedimentationActive && this.sedimentParticles) {
            this.updateSedimentation(deltaTime);
        }
        
        // 更新水流粒子
        if (this.waterFlowActive && this.waterFlow) {
            this.updateWaterFlow(deltaTime);
        }
        
        // 更新水位
        if (this.waterSurface) {
            const waterLevel = this.parameters.get('waterLevel');
            this.waterSurface.position.y = waterLevel;
        }
    }

    updateSedimentation(deltaTime) {
        const positions = this.sedimentParticles.geometry.attributes.position.array;
        const velocities = this.sedimentParticles.geometry.attributes.velocity.array;
        
        for (let i = 0; i < positions.length; i += 3) {
            // 更新位置
            positions[i] += velocities[i] * deltaTime;
            positions[i + 1] += velocities[i + 1] * deltaTime;
            positions[i + 2] += velocities[i + 2] * deltaTime;
            
            // 检查边界
            const distance = Math.sqrt(positions[i] * positions[i] + positions[i + 2] * positions[i + 2]);
            
            if (distance > 4.5 || positions[i + 1] <= 0.1) {
                // 重置粒子
                const radius = Math.random() * 0.7;
                const angle = Math.random() * Math.PI * 2;
                
                positions[i] = Math.cos(angle) * radius;
                positions[i + 1] = 2 + Math.random() * 0.5;
                positions[i + 2] = Math.sin(angle) * radius;
                
                velocities[i] = (Math.random() - 0.5) * 0.02;
                velocities[i + 1] = -0.1 - Math.random() * 0.1;
                velocities[i + 2] = (Math.random() - 0.5) * 0.02;
            }
        }
        
        this.sedimentParticles.geometry.attributes.position.needsUpdate = true;
    }

    updateWaterFlow(deltaTime) {
        const positions = this.waterFlow.geometry.attributes.position.array;
        const velocities = this.waterFlow.geometry.attributes.velocity.array;
        
        for (let i = 0; i < positions.length; i += 3) {
            positions[i] += velocities[i] * deltaTime;
            positions[i + 1] += velocities[i + 1] * deltaTime;
            positions[i + 2] += velocities[i + 2] * deltaTime;
            
            const distance = Math.sqrt(positions[i] * positions[i] + positions[i + 2] * positions[i + 2]);
            
            if (distance > 4.5) {
                // 重置到中心
                const radius = Math.random() * 0.7;
                const angle = Math.random() * Math.PI * 2;
                
                positions[i] = Math.cos(angle) * radius;
                positions[i + 1] = 1.5 + Math.random() * 0.5;
                positions[i + 2] = Math.sin(angle) * radius;
                
                const flowAngle = Math.random() * Math.PI * 2;
                velocities[i] = Math.cos(flowAngle) * 0.2;
                velocities[i + 1] = -0.02;
                velocities[i + 2] = Math.sin(flowAngle) * 0.2;
            }
        }
        
        this.waterFlow.geometry.attributes.position.needsUpdate = true;
    }

    getStatus() {
        const baseStatus = super.getStatus();
        return {
            ...baseStatus,
            retentionTime: this.parameters.get('retentionTime'),
            surfaceLoading: this.parameters.get('surfaceLoading'),
            waterLevel: this.parameters.get('waterLevel'),
            sludgeLevel: this.parameters.get('sludgeLevel'),
            scraperRotation: this.scraperRotation,
            removalEfficiency: this.getRemovalEfficiency(),
            ssRemoval: 60 + Math.random() * 15, // 模拟SS去除率
            bodRemoval: 25 + Math.random() * 10, // 模拟BOD去除率
            sludgeProduction: 40 + Math.random() * 20 // 模拟污泥产量
        };
    }

    getRemovalEfficiency() {
        const baseEfficiency = 0.65; // 基础效率65%
        const retentionFactor = Math.min(1.2, this.parameters.get('retentionTime') / 1.5);
        const loadingFactor = Math.max(0.8, Math.min(1.1, 30 / this.parameters.get('surfaceLoading')));
        
        return baseEfficiency * retentionFactor * loadingFactor * this.efficiency;
    }
}
