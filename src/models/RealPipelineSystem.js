/**
 * 真实污水处理工艺流程管道系统
 * 根据实际工艺流程设计的管道连接
 */

import * as THREE from 'three';

export class RealPipelineSystem {
    constructor() {
        this.group = new THREE.Group();
        this.pipes = [];
        this.valves = [];
        this.flowParticles = null;
        this.isVisible = true;
        
        // 管道参数
        this.pipeRadius = 0.2;
        this.mainPipeRadius = 0.3;
        this.pipeColor = 0x34495e;
        this.valveColor = 0xe74c3c;
        this.flowSpeed = 1.0;
        
        // 工艺流程路径定义（按用户指定的正确顺序）
        this.processFlow = [
            // 主流程：原水设备 → 调节池 → 细格栅 → 选择池 → 反应池 → 二沉池 → 二级产水池 → 砂滤罐 → 加氯罐 → 三级产水池 → 总出水设备
            { from: 'rawWaterTank', to: 'adjustmentTank', type: 'main' },
            { from: 'adjustmentTank', to: 'fineGrating', type: 'main' },
            { from: 'fineGrating', to: 'selectionTank', type: 'main' },
            { from: 'selectionTank', to: 'reactionTanks', type: 'main' },
            { from: 'reactionTanks', to: 'secondaryClarifier', type: 'main' },
            { from: 'secondaryClarifier', to: 'clearWaterTank', type: 'main' },
            { from: 'clearWaterTank', to: 'sandFilter', type: 'main' },
            { from: 'sandFilter', to: 'chlorinationTank', type: 'main' },
            { from: 'chlorinationTank', to: 'tertiaryClarifier', type: 'main' },
            { from: 'tertiaryClarifier', to: 'finalOutletTank', type: 'main' },

            // 附属关系
            { from: 'carbonSourceTank', to: 'selectionTank', type: 'branch' },
            { from: 'secondaryClarifier', to: 'sludgeTank', type: 'sludge' }
        ];
    }

    getAnchors() {
        // 原水与总出水等锚点坐标（与新设备布局匹配）
        return {
            rawWater: new THREE.Vector3(-90, 0, 0),  // 原水设备位置
            finalOut: new THREE.Vector3(130, 0, 0)   // 总出水设备位置
        };
    }

    async create(equipmentManager) {
        this.equipmentManager = equipmentManager;
        
        // 创建主流程管道
        this.createMainFlowPipes();
        
        // 创建支管
        this.createBranchPipes();
        
        // 创建污泥管道
        this.createSludgePipes();
        
        // 创建阀门
        this.createValves();
        
        // 创建流向指示
        this.createFlowIndicators();
        
        console.log('真实工艺流程管道系统创建完成');
    }

    createMainFlowPipes() {
        // 主流程管道路径
        const mainFlowConnections = this.processFlow.filter(conn => conn.type === 'main');
        
        mainFlowConnections.forEach(connection => {
            this.createPipeConnection(connection, this.mainPipeRadius, 0x2c3e50);
        });
    }

    createBranchPipes() {
        // 支管连接（如碳源投加）
        const branchConnections = this.processFlow.filter(conn => conn.type === 'branch');
        
        branchConnections.forEach(connection => {
            this.createPipeConnection(connection, this.pipeRadius, 0x8B4513);
        });
    }

    createSludgePipes() {
        // 污泥管道
        const sludgeConnections = this.processFlow.filter(conn => conn.type === 'sludge');
        
        sludgeConnections.forEach(connection => {
            this.createPipeConnection(connection, this.pipeRadius, 0x8B4513);
        });
    }

    createPipeConnection(connection, radius, color) {
        const fromEquipment = this.equipmentManager.getEquipment(connection.from);
        const toEquipment = this.equipmentManager.getEquipment(connection.to);
        
        if (!fromEquipment || !toEquipment) {
            console.warn(`设备未找到: ${connection.from} -> ${connection.to}`);
            return;
        }

        const fromPos = fromEquipment.position.clone();
        const toPos = toEquipment.position.clone();
        
        // 调整连接点高度
        fromPos.y += 1;
        toPos.y += 1;
        
        // 创建管道几何体
        const direction = new THREE.Vector3().subVectors(toPos, fromPos);
        const length = direction.length();
        
        // 直管段
        if (Math.abs(fromPos.y - toPos.y) < 0.5 && Math.abs(fromPos.z - toPos.z) < 0.5) {
            this.createStraightPipe(fromPos, toPos, radius, color);
        } else {
            // 弯管段
            this.createBentPipe(fromPos, toPos, radius, color);
        }
    }

    createStraightPipe(fromPos, toPos, radius, color) {
        const direction = new THREE.Vector3().subVectors(toPos, fromPos);
        const length = direction.length();
        
        const geometry = new THREE.CylinderGeometry(radius, radius, length);
        const material = new THREE.MeshLambertMaterial({ color: color });
        const pipe = new THREE.Mesh(geometry, material);
        
        // 定位和旋转
        pipe.position.copy(fromPos).add(toPos).multiplyScalar(0.5);
        pipe.lookAt(toPos);
        pipe.rotateX(Math.PI / 2);
        
        this.group.add(pipe);
        this.pipes.push(pipe);
    }

    createBentPipe(fromPos, toPos, radius, color) {
        // 创建L型弯管
        const midPoint = new THREE.Vector3(
            toPos.x,
            fromPos.y,
            fromPos.z
        );
        
        // 第一段：水平段
        this.createStraightPipe(fromPos, midPoint, radius, color);
        
        // 第二段：垂直或倾斜段
        this.createStraightPipe(midPoint, toPos, radius, color);
        
        // 在转弯处添加弯头
        this.createElbow(midPoint, radius, color);
    }

    createElbow(position, radius, color) {
        // 不再创建弯头球体，保持管道系统简洁
        // 管道连接依然正常，只是不显示球体装饰
        console.log('管道弯头球体已移除');
    }

    createValves() {
        // 在关键位置创建阀门
        const valvePositions = [
            { equipment: 'liftPumps', offset: new THREE.Vector3(2, 1, 0) },
            { equipment: 'reactionTanks', offset: new THREE.Vector3(-2, 1, 0) },
            { equipment: 'secondaryClarifier', offset: new THREE.Vector3(2, 1, 0) },
            { equipment: 'sandFilter', offset: new THREE.Vector3(-2, 1, 0) },
            { equipment: 'chlorinationTank', offset: new THREE.Vector3(-2, 1, 0) }
        ];

        valvePositions.forEach(valvePos => {
            const equipment = this.equipmentManager.getEquipment(valvePos.equipment);
            if (equipment) {
                const position = equipment.position.clone().add(valvePos.offset);
                this.createValve(position);
            }
        });
    }

    createValve(position) {
        const valveGroup = new THREE.Group();
        
        // 阀体
        const bodyGeometry = new THREE.CylinderGeometry(0.3, 0.3, 0.6);
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: this.valveColor });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        valveGroup.add(body);
        
        // 手轮
        const wheelGeometry = new THREE.CylinderGeometry(0.4, 0.4, 0.1);
        const wheelMaterial = new THREE.MeshLambertMaterial({ color: 0xff9900 });
        const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
        wheel.position.y = 0.35;
        valveGroup.add(wheel);
        
        valveGroup.position.copy(position);
        this.group.add(valveGroup);
        this.valves.push(valveGroup);
    }

    createFlowIndicators() {
        // 在管道上创建流向箭头
        this.pipes.forEach((pipe, index) => {
            if (index % 3 === 0) { // 每隔几根管道添加一个箭头
                this.createFlowArrow(pipe.position);
            }
        });
    }

    createFlowArrow(position) {
        const arrowGeometry = new THREE.ConeGeometry(0.1, 0.3);
        const arrowMaterial = new THREE.MeshLambertMaterial({ color: 0x00ff00 });
        const arrow = new THREE.Mesh(arrowGeometry, arrowMaterial);
        
        arrow.position.copy(position);
        arrow.position.y += 0.5;
        arrow.rotation.z = -Math.PI / 2; // 指向右侧
        
        this.group.add(arrow);
    }

    // 显示控制
    setVisible(visible) {
        this.isVisible = visible;
        this.group.visible = visible;
    }

    // 设置流速
    setFlowSpeed(speed) {
        this.flowSpeed = speed;
    }

    // 更新方法
    update(deltaTime) {
        if (this.isVisible) {
            // 旋转阀门手轮
            this.valves.forEach(valve => {
                const wheel = valve.children[1];
                if (wheel) {
                    wheel.rotation.y += deltaTime * 0.5;
                }
            });
        }
    }

    // 获取管道统计信息
    getStats() {
        return {
            pipeCount: this.pipes.length,
            valveCount: this.valves.length,
            totalLength: this.pipes.reduce((total, pipe) => {
                return total + pipe.geometry.parameters.height;
            }, 0)
        };
    }

    // 资源清理
    dispose() {
        this.group.traverse((child) => {
            if (child.geometry) child.geometry.dispose();
            if (child.material) {
                if (child.material.map) child.material.map.dispose();
                child.material.dispose();
            }
        });
        
        this.pipes.length = 0;
        this.valves.length = 0;
    }
}
