/**
 * 生化池 - 生物处理单元，包含厌氧、缺氧、好氧区
 */

import * as THREE from 'three';
import { Equipment } from './Equipment.js';

export class BiologicalTank extends Equipment {
    constructor(position) {
        super(position);
        
        // 生化池特有参数
        this.parameters.set('dissolvedOxygen', 2.5); // 溶解氧 (mg/L)
        this.parameters.set('mlss', 3500); // 混合液悬浮固体 (mg/L)
        this.parameters.set('svi', 120); // 污泥体积指数
        this.parameters.set('temperature', 25); // 温度 (°C)
        this.parameters.set('ph', 7.2); // pH值
        this.parameters.set('aerationRate', 80); // 曝气量 (m³/h)
        
        // 区域划分
        this.zones = {
            anaerobic: null,    // 厌氧区
            anoxic: null,       // 缺氧区
            aerobic: null       // 好氧区
        };
        
        // 曝气系统
        this.aerators = [];
        this.bubbleParticles = null;
        
        // 搅拌器
        this.mixers = [];
        this.mixerRotations = [];
    }

    async create() {
        this.createBasicMaterials();
        this.createBasicGeometries();
        
        // 创建主体结构
        this.createMainStructure();
        
        // 创建分区
        this.createZones();
        
        // 创建曝气系统
        this.createAerationSystem();
        
        // 创建搅拌系统
        this.createMixingSystem();
        
        // 创建监测设备
        this.createMonitoringEquipment();
        
        // 创建生物效果
        this.createBiologicalEffects();
        
        // 添加指示灯和标识
        this.createIndicatorLight(0x00ff00, new THREE.Vector3(0, 5, 0));
        this.createNamePlate('生化池', new THREE.Vector3(0, 5.5, 0));
        
        // 设置动画
        this.setupAnimations();
        
        this.updateBoundingBox();
    }

    createMainStructure() {
        // 主池体（矩形）
        const poolGeometry = new THREE.BoxGeometry(8, 4, 12);
        const poolMaterial = this.materials.get('concrete');
        
        const pool = this.addMesh(
            poolGeometry,
            poolMaterial,
            new THREE.Vector3(0, 2, 0)
        );
        pool.userData.isMainStructure = true;
        
        // 内壁
        const innerGeometry = new THREE.BoxGeometry(7.6, 3.8, 11.6);
        const innerMaterial = new THREE.MeshPhongMaterial({
            color: 0x95a5a6,
            side: THREE.BackSide
        });
        
        const innerWall = this.addMesh(
            innerGeometry,
            innerMaterial,
            new THREE.Vector3(0, 2, 0)
        );
        
        // 进水口
        const inletGeometry = new THREE.CylinderGeometry(0.4, 0.4, 1);
        const inletMaterial = this.materials.get('metal');
        
        const inlet = this.addMesh(
            inletGeometry,
            inletMaterial,
            new THREE.Vector3(-4.5, 2, -5),
            new THREE.Euler(0, 0, Math.PI / 2)
        );
        inlet.userData.isInlet = true;
        
        // 出水口
        const outlet = this.addMesh(
            inletGeometry,
            inletMaterial,
            new THREE.Vector3(4.5, 2, 5),
            new THREE.Euler(0, 0, Math.PI / 2)
        );
        outlet.userData.isOutlet = true;
        
        // 水面
        const waterGeometry = new THREE.PlaneGeometry(7.4, 11.4);
        const waterMaterial = this.materials.get('water');
        
        const water = this.addMesh(
            waterGeometry,
            waterMaterial,
            new THREE.Vector3(0, 3.8, 0),
            new THREE.Euler(-Math.PI / 2, 0, 0)
        );
        water.userData.isWater = true;
        
        this.waterSurface = water;
    }

    createZones() {
        // 厌氧区（深色）
        const anaerobicGeometry = new THREE.PlaneGeometry(7.2, 3.6);
        const anaerobicMaterial = new THREE.MeshPhongMaterial({
            color: 0x2c3e50,
            transparent: true,
            opacity: 0.3
        });
        
        const anaerobicZone = this.addMesh(
            anaerobicGeometry,
            anaerobicMaterial,
            new THREE.Vector3(0, 3.75, -4),
            new THREE.Euler(-Math.PI / 2, 0, 0)
        );
        this.zones.anaerobic = anaerobicZone;
        
        // 缺氧区（中等颜色）
        const anoxicMaterial = new THREE.MeshPhongMaterial({
            color: 0x34495e,
            transparent: true,
            opacity: 0.3
        });
        
        const anoxicZone = this.addMesh(
            anaerobicGeometry,
            anoxicMaterial,
            new THREE.Vector3(0, 3.75, 0),
            new THREE.Euler(-Math.PI / 2, 0, 0)
        );
        this.zones.anoxic = anoxicZone;
        
        // 好氧区（浅色）
        const aerobicMaterial = new THREE.MeshPhongMaterial({
            color: 0x3498db,
            transparent: true,
            opacity: 0.3
        });
        
        const aerobicZone = this.addMesh(
            anaerobicGeometry,
            aerobicMaterial,
            new THREE.Vector3(0, 3.75, 4),
            new THREE.Euler(-Math.PI / 2, 0, 0)
        );
        this.zones.aerobic = aerobicZone;
        
        // 分隔板
        const separatorGeometry = new THREE.PlaneGeometry(0.1, 3.5);
        const separatorMaterial = this.materials.get('concrete');
        
        const separator1 = this.addMesh(
            separatorGeometry,
            separatorMaterial,
            new THREE.Vector3(0, 2, -2),
            new THREE.Euler(0, 0, 0)
        );
        
        const separator2 = this.addMesh(
            separatorGeometry,
            separatorMaterial,
            new THREE.Vector3(0, 2, 2),
            new THREE.Euler(0, 0, 0)
        );
    }

    createAerationSystem() {
        // 曝气头（只在好氧区）
        const aeratorCount = 12;
        const aeratorGeometry = new THREE.CylinderGeometry(0.15, 0.15, 0.3);
        const aeratorMaterial = new THREE.MeshPhongMaterial({
            color: 0x7f8c8d
        });
        
        for (let i = 0; i < aeratorCount; i++) {
            const x = (i % 4 - 1.5) * 1.5;
            const z = Math.floor(i / 4) * 1.2 + 3;
            
            const aerator = this.addMesh(
                aeratorGeometry,
                aeratorMaterial,
                new THREE.Vector3(x, 0.35, z)
            );
            aerator.userData.isAerator = true;
            this.aerators.push(aerator);
        }
        
        // 曝气管道
        const pipeGeometry = new THREE.CylinderGeometry(0.1, 0.1, 6);
        const pipeMaterial = this.materials.get('metal');
        
        const mainPipe = this.addMesh(
            pipeGeometry,
            pipeMaterial,
            new THREE.Vector3(0, 0.2, 4),
            new THREE.Euler(0, 0, Math.PI / 2)
        );
        
        // 鼓风机
        const blowerGeometry = new THREE.CylinderGeometry(0.8, 0.8, 1.5);
        const blowerMaterial = new THREE.MeshPhongMaterial({
            color: 0x2980b9
        });
        
        const blower = this.addMesh(
            blowerGeometry,
            blowerMaterial,
            new THREE.Vector3(6, 1.75, 4)
        );
        blower.userData.isBlower = true;
        
        this.blower = blower;
    }

    createMixingSystem() {
        // 搅拌器（厌氧区和缺氧区）
        const mixerPositions = [
            new THREE.Vector3(0, 1, -4),  // 厌氧区
            new THREE.Vector3(0, 1, 0)    // 缺氧区
        ];
        
        mixerPositions.forEach((position, index) => {
            const mixerGroup = new THREE.Group();
            
            // 搅拌轴
            const shaftGeometry = new THREE.CylinderGeometry(0.08, 0.08, 2.5);
            const shaftMaterial = this.materials.get('metal');
            
            const shaft = new THREE.Mesh(shaftGeometry, shaftMaterial);
            shaft.position.set(0, 1.25, 0);
            mixerGroup.add(shaft);
            
            // 搅拌叶片
            const bladeGeometry = new THREE.BoxGeometry(1.5, 0.1, 0.3);
            const bladeMaterial = new THREE.MeshPhongMaterial({
                color: 0x34495e
            });
            
            const blade1 = new THREE.Mesh(bladeGeometry, bladeMaterial);
            blade1.position.set(0, 0.5, 0);
            mixerGroup.add(blade1);
            
            const blade2 = new THREE.Mesh(bladeGeometry, bladeMaterial);
            blade2.position.set(0, 0.5, 0);
            blade2.rotation.y = Math.PI / 2;
            mixerGroup.add(blade2);
            
            // 电机
            const motorGeometry = new THREE.CylinderGeometry(0.3, 0.3, 0.6);
            const motorMaterial = new THREE.MeshPhongMaterial({
                color: 0x3498db
            });
            
            const motor = new THREE.Mesh(motorGeometry, motorMaterial);
            motor.position.set(0, 3, 0);
            mixerGroup.add(motor);
            
            mixerGroup.position.copy(position);
            this.group.add(mixerGroup);
            
            this.mixers.push(mixerGroup);
            this.mixerRotations.push(0);
        });
    }

    createMonitoringEquipment() {
        // DO探头（溶解氧）
        const probeGeometry = new THREE.CylinderGeometry(0.05, 0.05, 1);
        const probeMaterial = new THREE.MeshPhongMaterial({
            color: 0xf39c12
        });
        
        const doProbe = this.addMesh(
            probeGeometry,
            probeMaterial,
            new THREE.Vector3(2, 2.5, 4)
        );
        doProbe.userData.isDOProbe = true;
        
        // pH探头
        const phProbe = this.addMesh(
            probeGeometry,
            probeMaterial,
            new THREE.Vector3(-2, 2.5, 0)
        );
        phProbe.userData.isPHProbe = true;
        
        // 温度探头
        const tempProbe = this.addMesh(
            probeGeometry,
            probeMaterial,
            new THREE.Vector3(0, 2.5, -4)
        );
        tempProbe.userData.isTempProbe = true;
        
        // 监测仪表箱
        const instrumentBoxGeometry = new THREE.BoxGeometry(1.5, 1, 0.3);
        const instrumentBoxMaterial = new THREE.MeshPhongMaterial({
            color: 0x34495e
        });
        
        const instrumentBox = this.addMesh(
            instrumentBoxGeometry,
            instrumentBoxMaterial,
            new THREE.Vector3(5, 3, 0)
        );
        
        this.monitoringEquipment = {
            doProbe,
            phProbe,
            tempProbe,
            instrumentBox
        };
    }

    createBiologicalEffects() {
        // 气泡效果（好氧区）
        this.createBubbleEffect();
        
        // 污泥悬浮效果
        this.createSludgeEffect();
        
        // 微生物活动效果
        this.createMicrobialEffect();
    }

    createBubbleEffect() {
        const bubbleCount = 800;
        const geometry = new THREE.BufferGeometry();
        
        const positions = new Float32Array(bubbleCount * 3);
        const velocities = new Float32Array(bubbleCount * 3);
        const sizes = new Float32Array(bubbleCount);
        const colors = new Float32Array(bubbleCount * 3);
        
        for (let i = 0; i < bubbleCount; i++) {
            const i3 = i * 3;
            
            // 从曝气头位置开始
            const aeratorIndex = Math.floor(Math.random() * this.aerators.length);
            const aerator = this.aerators[aeratorIndex];
            
            positions[i3] = aerator.position.x + (Math.random() - 0.5) * 0.3;
            positions[i3 + 1] = 0.5;
            positions[i3 + 2] = aerator.position.z + (Math.random() - 0.5) * 0.3;
            
            // 上升速度
            velocities[i3] = (Math.random() - 0.5) * 0.1;
            velocities[i3 + 1] = 0.5 + Math.random() * 0.5;
            velocities[i3 + 2] = (Math.random() - 0.5) * 0.1;
            
            // 大小
            sizes[i] = 0.02 + Math.random() * 0.03;
            
            // 颜色（白色气泡）
            const color = new THREE.Color(0xffffff);
            colors[i3] = color.r;
            colors[i3 + 1] = color.g;
            colors[i3 + 2] = color.b;
        }
        
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));
        geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        
        const material = new THREE.PointsMaterial({
            size: 0.05,
            transparent: true,
            opacity: 0.6,
            vertexColors: true,
            blending: THREE.AdditiveBlending
        });
        
        this.bubbleParticles = new THREE.Points(geometry, material);
        this.group.add(this.bubbleParticles);
    }

    createSludgeEffect() {
        // 活性污泥悬浮粒子
        const sludgeCount = 1000;
        const geometry = new THREE.BufferGeometry();
        
        const positions = new Float32Array(sludgeCount * 3);
        const velocities = new Float32Array(sludgeCount * 3);
        const colors = new Float32Array(sludgeCount * 3);
        
        for (let i = 0; i < sludgeCount; i++) {
            const i3 = i * 3;
            
            // 随机分布在池中
            positions[i3] = (Math.random() - 0.5) * 7;
            positions[i3 + 1] = 0.5 + Math.random() * 3;
            positions[i3 + 2] = (Math.random() - 0.5) * 11;
            
            // 缓慢流动
            velocities[i3] = (Math.random() - 0.5) * 0.05;
            velocities[i3 + 1] = (Math.random() - 0.5) * 0.02;
            velocities[i3 + 2] = (Math.random() - 0.5) * 0.05;
            
            // 棕色污泥
            const color = new THREE.Color();
            color.setHSL(0.08, 0.6, 0.3 + Math.random() * 0.2);
            colors[i3] = color.r;
            colors[i3 + 1] = color.g;
            colors[i3 + 2] = color.b;
        }
        
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        
        const material = new THREE.PointsMaterial({
            size: 0.03,
            transparent: true,
            opacity: 0.7,
            vertexColors: true
        });
        
        this.sludgeParticles = new THREE.Points(geometry, material);
        this.group.add(this.sludgeParticles);
    }

    createMicrobialEffect() {
        // 微生物活动的视觉效果（小光点）
        const microbialCount = 500;
        const geometry = new THREE.BufferGeometry();
        
        const positions = new Float32Array(microbialCount * 3);
        const colors = new Float32Array(microbialCount * 3);
        const sizes = new Float32Array(microbialCount);
        
        for (let i = 0; i < microbialCount; i++) {
            const i3 = i * 3;
            
            positions[i3] = (Math.random() - 0.5) * 7;
            positions[i3 + 1] = 0.5 + Math.random() * 3;
            positions[i3 + 2] = (Math.random() - 0.5) * 11;
            
            // 微生物发光效果
            const color = new THREE.Color();
            color.setHSL(0.3 + Math.random() * 0.3, 0.8, 0.6);
            colors[i3] = color.r;
            colors[i3 + 1] = color.g;
            colors[i3 + 2] = color.b;
            
            sizes[i] = 0.01 + Math.random() * 0.02;
        }
        
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
        
        const material = new THREE.PointsMaterial({
            size: 0.02,
            transparent: true,
            opacity: 0.8,
            vertexColors: true,
            blending: THREE.AdditiveBlending
        });
        
        this.microbialParticles = new THREE.Points(geometry, material);
        this.group.add(this.microbialParticles);
    }

    setupAnimations() {
        // 曝气动画
        this.addAnimation('aeration', (start) => {
            this.aerationActive = start;
        });
        
        // 搅拌动画
        this.addAnimation('mixing', (start) => {
            this.mixingActive = start;
        });
        
        // 生物活动动画
        this.addAnimation('biological', (start) => {
            this.biologicalActive = start;
        });
    }

    updateEquipment(deltaTime) {
        // 更新搅拌器旋转
        if (this.mixingActive) {
            this.mixers.forEach((mixer, index) => {
                this.mixerRotations[index] += 0.02 * deltaTime * 60;
                mixer.children[1].rotation.y = this.mixerRotations[index]; // 叶片1
                mixer.children[2].rotation.y = this.mixerRotations[index]; // 叶片2
            });
        }
        
        // 更新气泡效果
        if (this.aerationActive && this.bubbleParticles) {
            this.updateBubbles(deltaTime);
        }
        
        // 更新污泥悬浮
        if (this.biologicalActive && this.sludgeParticles) {
            this.updateSludge(deltaTime);
        }
        
        // 更新微生物效果
        if (this.biologicalActive && this.microbialParticles) {
            this.updateMicrobial(deltaTime);
        }
    }

    updateBubbles(deltaTime) {
        const positions = this.bubbleParticles.geometry.attributes.position.array;
        const velocities = this.bubbleParticles.geometry.attributes.velocity.array;
        
        for (let i = 0; i < positions.length; i += 3) {
            // 更新位置
            positions[i] += velocities[i] * deltaTime;
            positions[i + 1] += velocities[i + 1] * deltaTime;
            positions[i + 2] += velocities[i + 2] * deltaTime;
            
            // 重置到达水面的气泡
            if (positions[i + 1] > 3.8) {
                const aeratorIndex = Math.floor(Math.random() * this.aerators.length);
                const aerator = this.aerators[aeratorIndex];
                
                positions[i] = aerator.position.x + (Math.random() - 0.5) * 0.3;
                positions[i + 1] = 0.5;
                positions[i + 2] = aerator.position.z + (Math.random() - 0.5) * 0.3;
                
                velocities[i] = (Math.random() - 0.5) * 0.1;
                velocities[i + 1] = 0.5 + Math.random() * 0.5;
                velocities[i + 2] = (Math.random() - 0.5) * 0.1;
            }
        }
        
        this.bubbleParticles.geometry.attributes.position.needsUpdate = true;
    }

    updateSludge(deltaTime) {
        const positions = this.sludgeParticles.geometry.attributes.position.array;
        const velocities = this.sludgeParticles.geometry.attributes.velocity.array;
        
        for (let i = 0; i < positions.length; i += 3) {
            positions[i] += velocities[i] * deltaTime;
            positions[i + 1] += velocities[i + 1] * deltaTime;
            positions[i + 2] += velocities[i + 2] * deltaTime;
            
            // 边界检查
            if (Math.abs(positions[i]) > 3.5) {
                velocities[i] *= -1;
            }
            if (Math.abs(positions[i + 2]) > 5.5) {
                velocities[i + 2] *= -1;
            }
            if (positions[i + 1] < 0.5 || positions[i + 1] > 3.5) {
                velocities[i + 1] *= -1;
            }
        }
        
        this.sludgeParticles.geometry.attributes.position.needsUpdate = true;
    }

    updateMicrobial(deltaTime) {
        const time = Date.now() * 0.001;
        const colors = this.microbialParticles.geometry.attributes.color.array;
        
        for (let i = 0; i < colors.length; i += 3) {
            const intensity = 0.5 + 0.5 * Math.sin(time * 2 + i * 0.1);
            colors[i] *= intensity;
            colors[i + 1] *= intensity;
            colors[i + 2] *= intensity;
        }
        
        this.microbialParticles.geometry.attributes.color.needsUpdate = true;
    }

    getStatus() {
        const baseStatus = super.getStatus();
        return {
            ...baseStatus,
            dissolvedOxygen: this.parameters.get('dissolvedOxygen'),
            mlss: this.parameters.get('mlss'),
            svi: this.parameters.get('svi'),
            temperature: this.parameters.get('temperature'),
            ph: this.parameters.get('ph'),
            aerationRate: this.parameters.get('aerationRate'),
            biologicalActivity: this.biologicalActive ? 'Active' : 'Inactive',
            treatmentEfficiency: this.getTreatmentEfficiency(),
            codRemoval: 85 + Math.random() * 10, // 模拟COD去除率
            bodRemoval: 90 + Math.random() * 8,  // 模拟BOD去除率
            nitrificationRate: 75 + Math.random() * 15 // 模拟硝化率
        };
    }

    getTreatmentEfficiency() {
        const doFactor = Math.min(1, this.parameters.get('dissolvedOxygen') / 2.0);
        const tempFactor = Math.max(0.5, Math.min(1.2, this.parameters.get('temperature') / 25));
        const phFactor = Math.max(0.7, Math.min(1.1, 1 - Math.abs(this.parameters.get('ph') - 7) * 0.1));
        
        return 0.85 * doFactor * tempFactor * phFactor * this.efficiency;
    }
}
