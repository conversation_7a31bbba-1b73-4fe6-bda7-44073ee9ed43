/**
 * 砂滤罐设备类 (F-03)
 * 用于过滤处理
 */

import * as THREE from 'three';
import { Equipment } from './Equipment.js';

export class SandFilter extends Equipment {
    constructor(position, config = {}) {
        super(position);
        
        this.config = {
            radius: config.radius || 3,
            height: config.height || 6,
            name: config.name || '砂滤罐 (F-03)',
            id: config.id || 'F-03',
            sensors: config.sensors || ['DPIS301'],
            valves: config.valves || ['XV301', 'XV302', 'XV303']
        };
        
        this.isRunning = false;
        this.isBackwashing = false;
        this.pressureDiff = 0.5; // 压差值
        this.filterMesh = null;
        this.sandMesh = null;
    }

    async create() {
        this.group = new THREE.Group();
        
        // 创建主体结构
        await this.createMainStructure();
        
        // 创建砂层
        await this.createSandLayer();
        
        // 创建传感器
        await this.createSensors();
        
        // 创建阀门
        await this.createValves();
        
        // 创建管道连接
        await this.createPipes();
        
        // 设置位置
        this.group.position.copy(this.position);
        
        return this.group;
    }

    async createMainStructure() {
        // 圆柱形外壳
        const shellGeometry = new THREE.CylinderGeometry(
            this.config.radius, 
            this.config.radius, 
            this.config.height
        );
        const shellMaterial = new THREE.MeshLambertMaterial({
            color: 0x888888,
            transparent: true,
            opacity: 0.4
        });
        this.filterMesh = new THREE.Mesh(shellGeometry, shellMaterial);
        this.filterMesh.position.y = this.config.height / 2;
        this.group.add(this.filterMesh);

        // 边框
        const edges = new THREE.EdgesGeometry(shellGeometry);
        const lineMaterial = new THREE.LineBasicMaterial({ color: 0x333333 });
        const wireframe = new THREE.LineSegments(edges, lineMaterial);
        wireframe.position.y = this.config.height / 2;
        this.group.add(wireframe);

        // 底部支撑
        const baseGeometry = new THREE.CylinderGeometry(
            this.config.radius + 0.2, 
            this.config.radius + 0.2, 
            0.3
        );
        const baseMaterial = new THREE.MeshLambertMaterial({ color: 0x666666 });
        const baseMesh = new THREE.Mesh(baseGeometry, baseMaterial);
        baseMesh.position.y = 0.15;
        this.group.add(baseMesh);
    }

    async createSandLayer() {
        // 砂层
        const sandGeometry = new THREE.CylinderGeometry(
            this.config.radius - 0.2,
            this.config.radius - 0.2,
            this.config.height * 0.6
        );
        const sandMaterial = new THREE.MeshLambertMaterial({ 
            color: 0xc4a484,
            transparent: true,
            opacity: 0.8
        });
        this.sandMesh = new THREE.Mesh(sandGeometry, sandMaterial);
        this.sandMesh.position.y = this.config.height * 0.3 + 0.3;
        this.group.add(this.sandMesh);

        // 水层
        const waterGeometry = new THREE.CylinderGeometry(
            this.config.radius - 0.2,
            this.config.radius - 0.2,
            this.config.height * 0.3
        );
        const waterMaterial = new THREE.MeshLambertMaterial({
            color: 0x4a90e2,
            transparent: true,
            opacity: 0.6
        });
        const waterMesh = new THREE.Mesh(waterGeometry, waterMaterial);
        waterMesh.position.y = this.config.height * 0.85;
        this.group.add(waterMesh);
    }

    async createSensors() {
        // 压差传感器 DPIS301
        const sensorGeometry = new THREE.BoxGeometry(0.3, 0.3, 0.3);
        const sensorMaterial = new THREE.MeshLambertMaterial({ color: 0xffff00 });
        const sensorMesh = new THREE.Mesh(sensorGeometry, sensorMaterial);
        
        sensorMesh.position.set(
            this.config.radius + 0.5,
            this.config.height / 2,
            0
        );
        
        this.group.add(sensorMesh);
        
        // 传感器标签
        this.createSensorLabel('DPIS301', sensorMesh.position);
    }

    async createValves() {
        // 创建阀门 XV301, XV302, XV303
        this.config.valves.forEach((valveId, index) => {
            const valveGeometry = new THREE.CylinderGeometry(0.2, 0.2, 0.8);
            const valveMaterial = new THREE.MeshLambertMaterial({ color: 0xff6600 });
            const valveMesh = new THREE.Mesh(valveGeometry, valveMaterial);
            
            // 阀门位置
            const angle = (index * 120) * Math.PI / 180;
            valveMesh.position.set(
                Math.cos(angle) * (this.config.radius + 1),
                1,
                Math.sin(angle) * (this.config.radius + 1)
            );
            valveMesh.rotation.z = Math.PI / 2;
            
            this.group.add(valveMesh);
            
            // 阀门标签
            this.createValveLabel(valveId, valveMesh.position);
        });
    }

    async createPipes() {
        // 进水管
        const inletGeometry = new THREE.CylinderGeometry(0.3, 0.3, 2);
        const pipeMaterial = new THREE.MeshLambertMaterial({ color: 0x666666 });
        const inletPipe = new THREE.Mesh(inletGeometry, pipeMaterial);
        inletPipe.position.set(-this.config.radius - 1, this.config.height - 1, 0);
        inletPipe.rotation.z = Math.PI / 2;
        this.group.add(inletPipe);

        // 出水管
        const outletPipe = new THREE.Mesh(inletGeometry, pipeMaterial);
        outletPipe.position.set(this.config.radius + 1, 1, 0);
        outletPipe.rotation.z = Math.PI / 2;
        this.group.add(outletPipe);

        // 反洗管
        const backwashPipe = new THREE.Mesh(inletGeometry, pipeMaterial);
        backwashPipe.position.set(0, this.config.height + 1, this.config.radius + 1);
        this.group.add(backwashPipe);
    }

    createSensorLabel(text, position) {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = 128;
        canvas.height = 32;
        
        context.font = 'Bold 14px Arial';
        context.fillStyle = 'yellow';
        context.textAlign = 'center';
        context.fillText(text, canvas.width / 2, canvas.height / 2 + 5);
        
        const texture = new THREE.CanvasTexture(canvas);
        const material = new THREE.SpriteMaterial({ map: texture });
        const sprite = new THREE.Sprite(material);
        sprite.scale.set(2, 0.5, 1);
        sprite.position.copy(position);
        sprite.position.y += 1;
        
        this.group.add(sprite);
    }

    createValveLabel(text, position) {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = 128;
        canvas.height = 32;
        
        context.font = 'Bold 14px Arial';
        context.fillStyle = 'orange';
        context.textAlign = 'center';
        context.fillText(text, canvas.width / 2, canvas.height / 2 + 5);
        
        const texture = new THREE.CanvasTexture(canvas);
        const material = new THREE.SpriteMaterial({ map: texture });
        const sprite = new THREE.Sprite(material);
        sprite.scale.set(2, 0.5, 1);
        sprite.position.copy(position);
        sprite.position.y += 1;
        
        this.group.add(sprite);
    }

    // 设备控制方法
    start() {
        this.isRunning = true;
        console.log(`${this.config.name} 开始过滤`);
    }

    stop() {
        this.isRunning = false;
        console.log(`${this.config.name} 停止过滤`);
    }

    startBackwash() {
        this.isBackwashing = true;
        console.log(`${this.config.name} 开始反洗`);
    }

    stopBackwash() {
        this.isBackwashing = false;
        console.log(`${this.config.name} 停止反洗`);
    }

    setPressureDiff(value) {
        this.pressureDiff = value;
    }

    getStatus() {
        return {
            name: this.config.name,
            id: this.config.id,
            isRunning: this.isRunning,
            isBackwashing: this.isBackwashing,
            pressureDiff: this.pressureDiff,
            sensors: this.config.sensors,
            valves: this.config.valves
        };
    }

    update(deltaTime) {
        if (this.isRunning && this.sandMesh) {
            // 过滤过程中的轻微振动
            this.sandMesh.rotation.y += deltaTime * 0.1;
        }
        
        if (this.isBackwashing && this.sandMesh) {
            // 反洗时的强烈振动
            this.sandMesh.position.y += Math.sin(Date.now() * 0.01) * 0.05;
        }
    }

    getBoundingBox() {
        const box = new THREE.Box3();
        box.setFromObject(this.group);
        return box;
    }

    dispose() {
        this.group.traverse((child) => {
            if (child.geometry) child.geometry.dispose();
            if (child.material) {
                if (child.material.map) child.material.map.dispose();
                child.material.dispose();
            }
        });
    }
}
