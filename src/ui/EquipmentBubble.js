/**
 * 设备数据气泡管理器 - 负责显示设备详细数据的气泡框
 */

export class EquipmentBubble {
    constructor() {
        this.bubble = document.getElementById('equipmentBubble');
        this.title = document.getElementById('bubbleTitle');
        this.content = document.getElementById('bubbleContent');
        this.closeBtn = document.getElementById('bubbleClose');
        
        this.isVisible = false;
        this.currentEquipment = null;
        
        this.setupEventListeners();
        this.setupEquipmentData();
    }

    setupEventListeners() {
        // 关闭按钮
        this.closeBtn.addEventListener('click', () => {
            this.hide();
        });

        // 点击气泡外部关闭
        document.addEventListener('click', (event) => {
            if (this.isVisible && !this.bubble.contains(event.target)) {
                this.hide();
            }
        });

        // ESC键关闭
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && this.isVisible) {
                this.hide();
            }
        });
    }

    setupEquipmentData() {
        // 定义每个设备的监测数据配置
        this.equipmentDataConfig = {
            'T-00': { // 调节池
                name: '调节池',
                parameters: [
                    { label: 'COD (化学需氧量)', key: 'cod', unit: 'mg/L', range: [180, 220] },
                    { label: 'BOD (生化需氧量)', key: 'bod', unit: 'mg/L', range: [120, 150] },
                    { label: 'SS (悬浮物)', key: 'ss', unit: 'mg/L', range: [80, 120] },
                    { label: 'pH值', key: 'ph', unit: '', range: [6.5, 8.5] },
                    { label: '水位', key: 'waterLevel', unit: 'm', range: [2.5, 4.0] },
                    { label: '温度', key: 'temperature', unit: '°C', range: [18, 25] }
                ]
            },
            'T-01': { // 选择池
                name: '选择池',
                parameters: [
                    { label: 'COD (化学需氧量)', key: 'cod', unit: 'mg/L', range: [150, 180] },
                    { label: 'BOD (生化需氧量)', key: 'bod', unit: 'mg/L', range: [100, 130] },
                    { label: 'SS (悬浮物)', key: 'ss', unit: 'mg/L', range: [60, 90] },
                    { label: 'MLSS (混合液悬浮固体)', key: 'mlss', unit: 'mg/L', range: [3000, 4000] },
                    { label: '溶解氧', key: 'dissolvedOxygen', unit: 'mg/L', range: [0.2, 0.8] },
                    { label: 'pH值', key: 'ph', unit: '', range: [6.8, 7.5] }
                ]
            },
            'R-01': { // 反应池
                name: '反应池',
                parameters: [
                    { label: 'COD (化学需氧量)', key: 'cod', unit: 'mg/L', range: [80, 120] },
                    { label: 'BOD (生化需氧量)', key: 'bod', unit: 'mg/L', range: [40, 70] },
                    { label: 'NH₃-N (氨氮)', key: 'nh3n', unit: 'mg/L', range: [15, 25] },
                    { label: 'MLSS (混合液悬浮固体)', key: 'mlss', unit: 'mg/L', range: [3500, 4500] },
                    { label: '溶解氧', key: 'dissolvedOxygen', unit: 'mg/L', range: [2.0, 4.0] },
                    { label: 'SVI (污泥体积指数)', key: 'svi', unit: 'mL/g', range: [80, 150] }
                ]
            },
            'C-01': { // 二沉池
                name: '二沉池',
                parameters: [
                    { label: 'COD (化学需氧量)', key: 'cod', unit: 'mg/L', range: [30, 60] },
                    { label: 'BOD (生化需氧量)', key: 'bod', unit: 'mg/L', range: [15, 30] },
                    { label: 'SS (悬浮物)', key: 'ss', unit: 'mg/L', range: [10, 25] },
                    { label: 'NH₃-N (氨氮)', key: 'nh3n', unit: 'mg/L', range: [5, 15] },
                    { label: '浊度', key: 'turbidity', unit: 'NTU', range: [5, 15] },
                    { label: '污泥界面高度', key: 'sludgeLevel', unit: 'm', range: [0.5, 1.2] }
                ]
            },
            'T-02': { // 二级产水池
                name: '二级产水池',
                parameters: [
                    { label: 'COD (化学需氧量)', key: 'cod', unit: 'mg/L', range: [20, 40] },
                    { label: 'BOD (生化需氧量)', key: 'bod', unit: 'mg/L', range: [8, 20] },
                    { label: 'SS (悬浮物)', key: 'ss', unit: 'mg/L', range: [5, 15] },
                    { label: 'NH₃-N (氨氮)', key: 'nh3n', unit: 'mg/L', range: [2, 8] },
                    { label: 'TP (总磷)', key: 'tp', unit: 'mg/L', range: [0.3, 1.0] },
                    { label: '处理效率', key: 'efficiency', unit: '%', range: [92, 98] }
                ]
            },
            'T-03': { // 三级产水池
                name: '三级产水池',
                parameters: [
                    { label: 'COD (化学需氧量)', key: 'cod', unit: 'mg/L', range: [15, 30] },
                    { label: 'BOD (生化需氧量)', key: 'bod', unit: 'mg/L', range: [5, 15] },
                    { label: 'SS (悬浮物)', key: 'ss', unit: 'mg/L', range: [3, 10] },
                    { label: 'NH₃-N (氨氮)', key: 'nh3n', unit: 'mg/L', range: [1, 5] },
                    { label: 'TP (总磷)', key: 'tp', unit: 'mg/L', range: [0.2, 0.8] },
                    { label: '水位 (LS-304)', key: 'waterLevel', unit: 'm', range: [2.5, 4.0] },
                    { label: '处理效率', key: 'efficiency', unit: '%', range: [95, 99] }
                ]
            },
            'COARSE-GRATING': { // 粗格栅
                name: '粗格栅',
                parameters: [
                    { label: '运行状态', key: 'status', unit: '', range: ['正常', '维护'] },
                    { label: '栅渣量', key: 'debris', unit: 'kg/h', range: [5, 15] },
                    { label: '水头损失', key: 'headLoss', unit: 'mm', range: [10, 50] },
                    { label: '清渣频率', key: 'cleaningFreq', unit: '次/h', range: [2, 6] },
                    { label: '电机电流', key: 'motorCurrent', unit: 'A', range: [8, 15] }
                ]
            },
            'F-01': { // 细格栅
                name: '细格栅',
                parameters: [
                    { label: '运行状态', key: 'status', unit: '', range: ['正常', '维护'] },
                    { label: '栅渣量', key: 'debris', unit: 'kg/h', range: [2, 8] },
                    { label: '水头损失', key: 'headLoss', unit: 'mm', range: [15, 60] },
                    { label: '清渣频率', key: 'cleaningFreq', unit: '次/h', range: [4, 10] },
                    { label: '电机电流', key: 'motorCurrent', unit: 'A', range: [5, 12] }
                ]
            },
            'P-00A': { // 提升泵
                name: '提升泵站',
                parameters: [
                    { label: '运行状态', key: 'status', unit: '', range: ['运行', '停止'] },
                    { label: '流量', key: 'flowRate', unit: 'm³/h', range: [80, 120] },
                    { label: '扬程', key: 'head', unit: 'm', range: [8, 12] },
                    { label: '功率', key: 'power', unit: 'kW', range: [15, 25] },
                    { label: '效率', key: 'efficiency', unit: '%', range: [75, 85] },
                    { label: '振动', key: 'vibration', unit: 'mm/s', range: [1, 4] }
                ]
            }
        };
    }

    show(equipment) {
        this.currentEquipment = equipment;
        const equipmentId = equipment.userData.id;
        const config = this.equipmentDataConfig[equipmentId];

        if (!config) {
            console.warn(`未找到设备 ${equipmentId} 的数据配置`);
            return;
        }

        // 设置标题（添加图标）
        const icon = this.getEquipmentIcon(equipmentId);
        this.title.textContent = `${icon} ${config.name}`;

        // 生成数据内容
        this.generateContent(config);

        // 定位气泡到右上角
        this.positionBubbleTopRight();

        // 显示气泡
        this.bubble.classList.add('show');
        this.isVisible = true;

        // 设置控制按钮事件
        this.setupControlButtons();

        // 开始数据更新
        this.startDataUpdate();
    }

    hide() {
        this.bubble.classList.remove('show');
        this.isVisible = false;
        this.currentEquipment = null;
        this.stopDataUpdate();
    }

    generateContent(config) {
        this.content.innerHTML = '';

        // 基本信息模块
        const basicInfoSection = this.createBasicInfoSection(config);
        this.content.appendChild(basicInfoSection);

        // 运行参数模块
        const parametersSection = this.createParametersSection(config);
        this.content.appendChild(parametersSection);

        // 操作控制模块
        const controlSection = this.createControlSection(config);
        this.content.appendChild(controlSection);
    }

    createBasicInfoSection(config) {
        const section = document.createElement('div');
        section.className = 'info-section';

        const equipment = this.currentEquipment.userData;
        const isRunning = Math.random() > 0.3; // 模拟运行状态

        section.innerHTML = `
            <div class="section-header">
                <span class="section-icon">📋</span>
                <span class="section-title">基本信息</span>
            </div>
            <div class="basic-info-grid">
                <div class="info-item">
                    <span class="info-label">设备类型:</span>
                    <span class="info-value">${equipment.type || 'tank'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">设备ID:</span>
                    <span class="info-value">${equipment.id}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">运行状态:</span>
                    <span class="info-value status-${isRunning ? 'running' : 'stopped'}">
                        ${isRunning ? '已启动' : '已停止'}
                    </span>
                </div>
            </div>
        `;

        return section;
    }

    createParametersSection(config) {
        const section = document.createElement('div');
        section.className = 'info-section';

        section.innerHTML = `
            <div class="section-header">
                <span class="section-icon">📊</span>
                <span class="section-title">运行参数</span>
            </div>
            <div class="parameters-grid" id="parametersGrid">
                <!-- 参数将在这里动态生成 -->
            </div>
            <div class="detail-link">
                <span class="link-text">查看详细参数信息</span>
            </div>
        `;

        // 生成参数列表
        const parametersGrid = section.querySelector('#parametersGrid');
        config.parameters.slice(0, 6).forEach(param => { // 显示前6个参数
            const dataItem = document.createElement('div');
            dataItem.className = 'param-item';

            const value = this.generateRandomValue(param);
            const status = this.getParameterStatus(value, param);

            dataItem.innerHTML = `
                <span class="param-label">${param.label}</span>
                <span class="param-value" id="param-${param.key}">
                    ${value}
                    <span class="param-unit">${param.unit}</span>
                </span>
                <span class="param-status ${status.class}">${status.text}</span>
            `;

            parametersGrid.appendChild(dataItem);
        });

        return section;
    }

    createControlSection(config) {
        const section = document.createElement('div');
        section.className = 'info-section';

        section.innerHTML = `
            <div class="section-header">
                <span class="section-icon">🎛️</span>
                <span class="section-title">操作控制</span>
            </div>
            <div class="control-buttons">
                <button class="control-btn start-btn" id="startBtn">启动</button>
                <button class="control-btn stop-btn" id="stopBtn">停止</button>
                <button class="control-btn reset-btn" id="resetBtn">重置</button>
            </div>
        `;

        return section;
    }

    generateRandomValue(param) {
        if (Array.isArray(param.range) && typeof param.range[0] === 'string') {
            // 状态类型参数
            return param.range[Math.floor(Math.random() * param.range.length)];
        } else {
            // 数值类型参数
            const min = param.range[0];
            const max = param.range[1];
            const value = min + Math.random() * (max - min);
            
            if (param.unit === '%') {
                return value.toFixed(1);
            } else if (param.key === 'ph') {
                return value.toFixed(2);
            } else {
                return value.toFixed(1);
            }
        }
    }

    getParameterStatus(value, param) {
        if (typeof value === 'string') {
            return value === '正常' || value === '运行' ? 
                { class: 'status-normal', text: '正常' } : 
                { class: 'status-warning', text: '注意' };
        }

        const numValue = parseFloat(value);
        const min = param.range[0];
        const max = param.range[1];
        const mid = (min + max) / 2;

        if (numValue >= min * 0.9 && numValue <= max * 1.1) {
            return { class: 'status-normal', text: '正常' };
        } else if (numValue >= min * 0.7 && numValue <= max * 1.3) {
            return { class: 'status-warning', text: '警告' };
        } else {
            return { class: 'status-danger', text: '异常' };
        }
    }

    positionBubbleTopRight() {
        // 确保固定定位到右上角
        const margin = 20;

        // 强制设置位置样式
        this.bubble.style.cssText = `
            position: fixed !important;
            top: ${margin}px !important;
            right: ${margin}px !important;
            left: auto !important;
            bottom: auto !important;
            width: 350px !important;
            min-height: 450px !important;
            max-height: calc(100vh - 40px) !important;
            z-index: 1500 !important;
        `;
    }

    positionBubble(mouseX, mouseY) {
        const bubbleRect = this.bubble.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        let left = mouseX + 20;
        let top = mouseY - bubbleRect.height / 2;

        // 防止气泡超出右边界
        if (left + bubbleRect.width > viewportWidth - 20) {
            left = mouseX - bubbleRect.width - 20;
        }

        // 防止气泡超出上下边界
        if (top < 20) {
            top = 20;
        } else if (top + bubbleRect.height > viewportHeight - 20) {
            top = viewportHeight - bubbleRect.height - 20;
        }

        this.bubble.style.left = `${left}px`;
        this.bubble.style.top = `${top}px`;
    }

    startDataUpdate() {
        this.stopDataUpdate();
        this.updateInterval = setInterval(() => {
            if (this.isVisible && this.currentEquipment) {
                this.updateData();
            }
        }, 2000); // 每2秒更新一次数据
    }

    stopDataUpdate() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }

    updateData() {
        const equipmentId = this.currentEquipment.userData.id;
        const config = this.equipmentDataConfig[equipmentId];

        if (!config) return;

        // 只更新显示的前6个参数
        config.parameters.slice(0, 6).forEach(param => {
            const element = document.getElementById(`param-${param.key}`);
            if (element) {
                const value = this.generateRandomValue(param);
                const status = this.getParameterStatus(value, param);

                element.innerHTML = `${value} <span class="param-unit">${param.unit}</span>`;

                // 更新状态
                const statusElement = element.parentElement.querySelector('.param-status');
                if (statusElement) {
                    statusElement.className = `param-status ${status.class}`;
                    statusElement.textContent = status.text;
                }
            }
        });
    }

    // 公共方法
    isShowing() {
        return this.isVisible;
    }

    getCurrentEquipment() {
        return this.currentEquipment;
    }

    getEquipmentIcon(equipmentId) {
        const iconMap = {
            'T-00': '🏊', // 调节池
            'T-01': '⚡', // 选择池
            'R-01': '🔄', // 反应池
            'C-01': '💧', // 二沉池
            'T-02': '🌊', // 二级产水池
            'T-03': '✨', // 三级产水池
            'COARSE-GRATING': '🔧', // 粗格栅
            'F-01': '⚙️', // 细格栅
            'P-00A': '🚰', // 提升泵
            'T-05': '🛢️', // 碳源罐
            'SF-01': '🏺', // 砂滤罐
            'CT-01': '💊'  // 加氯罐
        };

        return iconMap[equipmentId] || '📊';
    }

    setupControlButtons() {
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const resetBtn = document.getElementById('resetBtn');

        if (startBtn) {
            startBtn.addEventListener('click', () => this.startEquipment());
        }
        if (stopBtn) {
            stopBtn.addEventListener('click', () => this.stopEquipment());
        }
        if (resetBtn) {
            resetBtn.addEventListener('click', () => this.resetEquipment());
        }
    }

    startEquipment() {
        console.log(`启动设备: ${this.currentEquipment.userData.name}`);
        // 这里可以添加实际的设备启动逻辑
        this.showOperationFeedback('设备启动成功', 'success');
    }

    stopEquipment() {
        console.log(`停止设备: ${this.currentEquipment.userData.name}`);
        // 这里可以添加实际的设备停止逻辑
        this.showOperationFeedback('设备已停止', 'warning');
    }

    resetEquipment() {
        console.log(`重置设备: ${this.currentEquipment.userData.name}`);
        // 这里可以添加实际的设备重置逻辑
        this.showOperationFeedback('设备重置完成', 'info');
    }

    showOperationFeedback(message, type) {
        // 简单的操作反馈
        const feedback = document.createElement('div');
        feedback.className = `operation-feedback ${type}`;
        feedback.textContent = message;
        feedback.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: ${type === 'success' ? '#27ae60' : type === 'warning' ? '#f39c12' : '#3498db'};
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 2000;
            animation: slideInRight 0.3s ease;
        `;

        document.body.appendChild(feedback);

        setTimeout(() => {
            feedback.remove();
        }, 2000);
    }
}
