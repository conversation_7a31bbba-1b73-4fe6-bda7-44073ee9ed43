/**
 * UI管理器 - 管理用户界面交互和数据显示
 */

export class UIManager {
    constructor() {
        this.dataManager = null;
        this.isInitialized = false;
        this.updateInterval = null;
        this.charts = new Map();
        
        // UI元素引用
        this.elements = {
            controlPanel: document.getElementById('controlPanel'),
            infoPanel: document.getElementById('infoPanel'),
            loadingScreen: document.getElementById('loadingScreen')
        };
        
        // 当前选中的设备
        this.selectedEquipment = null;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeCharts();
        this.startDataUpdates();
        this.isInitialized = true;
    }

    setupEventListeners() {
        // 控制面板事件已在main.js中设置
        // 这里处理其他UI事件
        
        // 窗口大小变化
        window.addEventListener('resize', () => {
            this.handleResize();
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', (event) => {
            this.handleKeyboard(event);
        });
        
        // 面板拖拽功能
        this.makePanelsDraggable();
    }

    makePanelsDraggable() {
        const panels = [this.elements.controlPanel, this.elements.infoPanel];
        
        panels.forEach(panel => {
            if (!panel) return;
            
            let isDragging = false;
            let currentX;
            let currentY;
            let initialX;
            let initialY;
            let xOffset = 0;
            let yOffset = 0;

            // 添加拖拽手柄
            const dragHandle = document.createElement('div');
            dragHandle.className = 'drag-handle';
            dragHandle.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 30px;
                background: rgba(0,0,0,0.1);
                cursor: move;
                border-radius: 10px 10px 0 0;
            `;
            panel.insertBefore(dragHandle, panel.firstChild);

            dragHandle.addEventListener('mousedown', (e) => {
                initialX = e.clientX - xOffset;
                initialY = e.clientY - yOffset;

                if (e.target === dragHandle) {
                    isDragging = true;
                }
            });

            document.addEventListener('mousemove', (e) => {
                if (isDragging) {
                    e.preventDefault();
                    currentX = e.clientX - initialX;
                    currentY = e.clientY - initialY;

                    xOffset = currentX;
                    yOffset = currentY;

                    panel.style.transform = `translate3d(${currentX}px, ${currentY}px, 0)`;
                }
            });

            document.addEventListener('mouseup', () => {
                initialX = currentX;
                initialY = currentY;
                isDragging = false;
            });
        });
    }

    initializeCharts() {
        // 初始化小型图表
        const chartConfigs = [
            { id: 'cod-chart', label: 'COD', color: '#e74c3c' },
            { id: 'bod-chart', label: 'BOD', color: '#f39c12' },
            { id: 'ss-chart', label: 'SS', color: '#3498db' },
            { id: 'nh3n-chart', label: 'NH₃-N', color: '#27ae60' },
            { id: 'tp-chart', label: 'TP', color: '#9b59b6' },
            { id: 'efficiency-chart', label: '效率', color: '#1abc9c' }
        ];

        chartConfigs.forEach(config => {
            this.createMiniChart(config);
        });
    }

    createMiniChart(config) {
        const container = document.getElementById(config.id);
        if (!container) return;

        // 创建简单的SVG图表
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.setAttribute('width', '100%');
        svg.setAttribute('height', '100%');
        svg.style.background = 'rgba(255,255,255,0.1)';
        svg.style.borderRadius = '4px';

        // 创建数据线
        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        path.setAttribute('stroke', config.color);
        path.setAttribute('stroke-width', '2');
        path.setAttribute('fill', 'none');

        // 创建填充区域
        const area = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        area.setAttribute('fill', config.color);
        area.setAttribute('opacity', '0.2');

        svg.appendChild(area);
        svg.appendChild(path);
        container.appendChild(svg);

        // 存储图表引用
        this.charts.set(config.id, {
            svg: svg,
            path: path,
            area: area,
            data: [],
            maxPoints: 20
        });
    }

    updateChart(chartId, value) {
        const chart = this.charts.get(chartId);
        if (!chart) return;

        // 添加新数据点
        chart.data.push(value);
        if (chart.data.length > chart.maxPoints) {
            chart.data.shift();
        }

        // 更新图表
        this.redrawChart(chart);
    }

    redrawChart(chart) {
        const data = chart.data;
        if (data.length < 2) return;

        const width = 100; // 百分比宽度
        const height = 100; // 百分比高度
        const padding = 5;

        const minValue = Math.min(...data);
        const maxValue = Math.max(...data);
        const range = maxValue - minValue || 1;

        // 生成路径
        let pathData = '';
        let areaData = '';

        data.forEach((value, index) => {
            const x = padding + (index / (data.length - 1)) * (width - 2 * padding);
            const y = height - padding - ((value - minValue) / range) * (height - 2 * padding);

            if (index === 0) {
                pathData += `M ${x} ${y}`;
                areaData += `M ${x} ${height - padding}`;
                areaData += ` L ${x} ${y}`;
            } else {
                pathData += ` L ${x} ${y}`;
                areaData += ` L ${x} ${y}`;
            }
        });

        // 闭合填充区域
        const lastX = padding + (width - 2 * padding);
        areaData += ` L ${lastX} ${height - padding} Z`;

        chart.path.setAttribute('d', pathData);
        chart.area.setAttribute('d', areaData);
    }

    setDataManager(dataManager) {
        this.dataManager = dataManager;
    }

    startDataUpdates() {
        // 每2秒更新一次数据显示
        this.updateInterval = setInterval(() => {
            this.updateDataDisplay();
        }, 2000);
    }

    updateDataDisplay() {
        if (!this.dataManager) return;

        const data = this.dataManager.getCurrentData();
        
        // 更新数值显示
        this.updateParameterValue('cod-value', data.cod, 'mg/L');
        this.updateParameterValue('bod-value', data.bod, 'mg/L');
        this.updateParameterValue('ss-value', data.ss, 'mg/L');
        this.updateParameterValue('nh3n-value', data.nh3n, 'mg/L');
        this.updateParameterValue('tp-value', data.tp, 'mg/L');
        this.updateParameterValue('efficiency-value', data.efficiency, '%');

        // 更新图表
        this.updateChart('cod-chart', data.cod);
        this.updateChart('bod-chart', data.bod);
        this.updateChart('ss-chart', data.ss);
        this.updateChart('nh3n-chart', data.nh3n);
        this.updateChart('tp-chart', data.tp);
        this.updateChart('efficiency-chart', data.efficiency);
    }

    updateParameterValue(elementId, value, unit) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = `${value.toFixed(1)} ${unit}`;
            
            // 添加颜色指示
            element.className = this.getValueClass(elementId, value);
        }
    }

    getValueClass(elementId, value) {
        // 根据参数类型和值返回CSS类名
        const thresholds = {
            'cod-value': { good: 50, warning: 100 },
            'bod-value': { good: 20, warning: 50 },
            'ss-value': { good: 20, warning: 40 },
            'nh3n-value': { good: 5, warning: 15 },
            'tp-value': { good: 0.5, warning: 1.0 },
            'efficiency-value': { good: 95, warning: 85 }
        };

        const threshold = thresholds[elementId];
        if (!threshold) return '';

        if (elementId === 'efficiency-value') {
            // 效率值越高越好
            if (value >= threshold.good) return 'value-good';
            if (value >= threshold.warning) return 'value-warning';
            return 'value-bad';
        } else {
            // 其他参数值越低越好
            if (value <= threshold.good) return 'value-good';
            if (value <= threshold.warning) return 'value-warning';
            return 'value-bad';
        }
    }

    showEquipmentInfo(equipment) {
        this.selectedEquipment = equipment;
        const infoPanel = this.elements.infoPanel;
        
        if (!infoPanel || !equipment) return;

        // 显示信息面板
        infoPanel.style.display = 'block';
        
        // 更新标题
        const title = document.getElementById('infoTitle');
        if (title) {
            title.textContent = equipment.userData.name || '设备信息';
        }

        // 更新内容
        const content = document.getElementById('infoContent');
        if (content) {
            content.innerHTML = this.generateEquipmentInfo(equipment);
        }

        // 添加关闭按钮事件
        this.addCloseButtonToInfoPanel();
    }

    generateEquipmentInfo(equipment) {
        const userData = equipment.userData;
        const equipmentObj = userData.equipment;
        
        let html = `
            <div class="equipment-info">
                <h4>📋 基本信息</h4>
                <p><strong>设备类型:</strong> ${userData.type}</p>
                <p><strong>设备ID:</strong> ${userData.id}</p>
                <p><strong>运行状态:</strong> <span class="${equipmentObj?.isRunning ? 'status-running' : 'status-stopped'}">
                    ${equipmentObj?.isRunning ? '运行中' : '已停止'}
                </span></p>
        `;

        if (equipmentObj && equipmentObj.getStatus) {
            const status = equipmentObj.getStatus();
            
            html += `
                <h4>📊 运行参数</h4>
                <div class="parameter-list">
            `;

            // 根据设备类型显示不同参数
            switch (userData.type) {
                case 'pump':
                    html += `
                        <p><strong>流量:</strong> ${status.actualFlowRate?.toFixed(1) || 'N/A'} m³/h</p>
                        <p><strong>扬程:</strong> ${status.headPressure?.toFixed(1) || 'N/A'} m</p>
                        <p><strong>功耗:</strong> ${status.powerConsumption?.toFixed(1) || 'N/A'} kW</p>
                        <p><strong>效率:</strong> ${(status.pumpEfficiency * 100)?.toFixed(1) || 'N/A'}%</p>
                    `;
                    break;
                case 'clarifier':
                    html += `
                        <p><strong>去除效率:</strong> ${(status.removalEfficiency * 100)?.toFixed(1) || 'N/A'}%</p>
                        <p><strong>水位:</strong> ${status.waterLevel?.toFixed(2) || 'N/A'} m</p>
                        <p><strong>污泥层厚度:</strong> ${status.sludgeLevel?.toFixed(2) || 'N/A'} m</p>
                    `;
                    break;
                case 'biological':
                    html += `
                        <p><strong>溶解氧:</strong> ${status.dissolvedOxygen?.toFixed(1) || 'N/A'} mg/L</p>
                        <p><strong>MLSS:</strong> ${status.mlss || 'N/A'} mg/L</p>
                        <p><strong>温度:</strong> ${status.temperature?.toFixed(1) || 'N/A'} °C</p>
                        <p><strong>pH:</strong> ${status.ph?.toFixed(1) || 'N/A'}</p>
                    `;
                    break;
                case 'advanced':
                    html += `
                        <p><strong>总体效率:</strong> ${(status.overallEfficiency * 100)?.toFixed(1) || 'N/A'}%</p>
                        <p><strong>UV强度:</strong> ${status.uvIntensity || 'N/A'} mJ/cm²</p>
                        <p><strong>氯投加量:</strong> ${status.chlorineDosage?.toFixed(1) || 'N/A'} mg/L</p>
                    `;
                    break;
                default:
                    html += `<p>暂无详细参数信息</p>`;
            }

            html += `</div>`;
        }

        html += `
                <h4>🔧 操作控制</h4>
                <div class="control-buttons">
                    <button onclick="window.uiManager.controlEquipment('start')" class="btn-start">启动</button>
                    <button onclick="window.uiManager.controlEquipment('stop')" class="btn-stop">停止</button>
                    <button onclick="window.uiManager.controlEquipment('reset')" class="btn-reset">重置</button>
                </div>
            </div>
        `;

        return html;
    }

    addCloseButtonToInfoPanel() {
        const infoPanel = this.elements.infoPanel;
        if (!infoPanel) return;

        // 检查是否已有关闭按钮
        if (infoPanel.querySelector('.close-btn')) return;

        const closeBtn = document.createElement('button');
        closeBtn.className = 'close-btn';
        closeBtn.innerHTML = '✕';
        closeBtn.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #666;
        `;
        
        closeBtn.addEventListener('click', () => {
            this.hideEquipmentInfo();
        });

        infoPanel.appendChild(closeBtn);
    }

    hideEquipmentInfo() {
        const infoPanel = this.elements.infoPanel;
        if (infoPanel) {
            infoPanel.style.display = 'none';
        }
        this.selectedEquipment = null;
    }

    controlEquipment(action) {
        if (!this.selectedEquipment || !this.selectedEquipment.userData.equipment) return;

        const equipment = this.selectedEquipment.userData.equipment;

        switch (action) {
            case 'start':
                if (equipment.start) {
                    equipment.start();
                    this.showNotification('设备已启动', 'success');
                }
                break;
            case 'stop':
                if (equipment.stop) {
                    equipment.stop();
                    this.showNotification('设备已停止', 'info');
                }
                break;
            case 'reset':
                if (equipment.reset) {
                    equipment.reset();
                    this.showNotification('设备已重置', 'info');
                }
                break;
        }

        // 更新信息面板
        setTimeout(() => {
            this.showEquipmentInfo(this.selectedEquipment);
        }, 100);
    }

    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            z-index: 10000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;

        // 设置颜色
        const colors = {
            success: '#27ae60',
            error: '#e74c3c',
            warning: '#f39c12',
            info: '#3498db'
        };
        notification.style.background = colors[type] || colors.info;

        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 10);

        // 自动隐藏
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    handleResize() {
        // 响应式处理
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
            // 移动端布局调整
            this.elements.controlPanel.style.width = 'calc(100vw - 40px)';
        } else {
            // 桌面端布局
            this.elements.controlPanel.style.width = '280px';
        }
    }

    handleKeyboard(event) {
        // 键盘快捷键处理
        if (event.ctrlKey || event.metaKey) {
            switch (event.key) {
                case 'h':
                    event.preventDefault();
                    this.togglePanelVisibility();
                    break;
                case 'r':
                    event.preventDefault();
                    // 重置视角的快捷键在CameraController中处理
                    break;
            }
        }

        // ESC键关闭信息面板
        if (event.key === 'Escape') {
            this.hideEquipmentInfo();
        }
    }

    togglePanelVisibility() {
        const panels = [this.elements.controlPanel];
        panels.forEach(panel => {
            if (panel) {
                panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
            }
        });
    }

    dispose() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        
        this.charts.clear();
        this.selectedEquipment = null;
    }
}

// 全局访问
window.uiManager = null;
