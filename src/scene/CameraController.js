/**
 * 相机控制器 - 负责相机的移动、视角切换和动画
 */

import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import * as TWEEN from '@tweenjs/tween.js';

export class CameraController {
    constructor(camera, domElement) {
        this.camera = camera;
        this.domElement = domElement;
        
        // 预设相机位置
        this.presetPositions = {
            overview: {
                position: new THREE.Vector3(30, 20, 30),
                target: new THREE.Vector3(20, 0, 0),
                description: '全景视图'
            },
            detail: {
                position: new THREE.Vector3(10, 8, 15),
                target: new THREE.Vector3(10, 0, 0),
                description: '细节视图'
            },
            follow: {
                position: new THREE.Vector3(-25, 5, 5),
                target: new THREE.Vector3(-20, 0, 0),
                description: '跟随模式'
            },
            aerial: {
                position: new THREE.Vector3(20, 50, 0),
                target: new THREE.Vector3(20, 0, 0),
                description: '鸟瞰视图'
            },
            side: {
                position: new THREE.Vector3(20, 10, 40),
                target: new THREE.Vector3(20, 0, 0),
                description: '侧面视图'
            },
            entrance: {
                position: new THREE.Vector3(-30, 3, 0),
                target: new THREE.Vector3(-20, 0, 0),
                description: '入口视图'
            },
            exit: {
                position: new THREE.Vector3(65, 3, 0),
                target: new THREE.Vector3(55, 0, 0),
                description: '出口视图'
            }
        };

        this.currentMode = 'overview';
        this.isAnimating = false;
        this.followPath = null;
        this.followProgress = 0;
        this.followSpeed = 0.01;

        this.setupControls();
        this.setupKeyboardControls();
        this.createFollowPath();
    }

    setupControls() {
        this.controls = new OrbitControls(this.camera, this.domElement);
        
        // 基础控制设置
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.screenSpacePanning = false;
        
        // 缩放限制
        this.controls.minDistance = 5;
        this.controls.maxDistance = 200;
        
        // 垂直角度限制
        this.controls.maxPolarAngle = Math.PI / 2.1;
        this.controls.minPolarAngle = Math.PI / 6;
        
        // 水平角度限制（可选）
        // this.controls.minAzimuthAngle = -Math.PI / 2;
        // this.controls.maxAzimuthAngle = Math.PI / 2;
        
        // 平移限制
        this.controls.target.set(20, 0, 0);
        
        // 自动旋转（可选）
        this.controls.autoRotate = false;
        this.controls.autoRotateSpeed = 0.5;
        
        // 事件监听
        this.controls.addEventListener('start', () => {
            this.onControlStart();
        });
        
        this.controls.addEventListener('end', () => {
            this.onControlEnd();
        });
    }

    setupKeyboardControls() {
        this.keys = {
            forward: false,
            backward: false,
            left: false,
            right: false,
            up: false,
            down: false
        };

        document.addEventListener('keydown', (event) => {
            this.onKeyDown(event);
        });

        document.addEventListener('keyup', (event) => {
            this.onKeyUp(event);
        });
    }

    onKeyDown(event) {
        switch (event.code) {
            case 'KeyW':
            case 'ArrowUp':
                this.keys.forward = true;
                break;
            case 'KeyS':
            case 'ArrowDown':
                this.keys.backward = true;
                break;
            case 'KeyA':
            case 'ArrowLeft':
                this.keys.left = true;
                break;
            case 'KeyD':
            case 'ArrowRight':
                this.keys.right = true;
                break;
            case 'KeyQ':
                this.keys.up = true;
                break;
            case 'KeyE':
                this.keys.down = true;
                break;
            case 'Digit1':
                this.changeViewMode('overview');
                break;
            case 'Digit2':
                this.changeViewMode('detail');
                break;
            case 'Digit3':
                this.changeViewMode('follow');
                break;
            case 'Digit4':
                this.changeViewMode('aerial');
                break;
            case 'Digit5':
                this.changeViewMode('side');
                break;
            case 'KeyR':
                this.resetToOverview();
                break;
            case 'Space':
                event.preventDefault();
                this.toggleAutoRotate();
                break;
        }
    }

    onKeyUp(event) {
        switch (event.code) {
            case 'KeyW':
            case 'ArrowUp':
                this.keys.forward = false;
                break;
            case 'KeyS':
            case 'ArrowDown':
                this.keys.backward = false;
                break;
            case 'KeyA':
            case 'ArrowLeft':
                this.keys.left = false;
                break;
            case 'KeyD':
            case 'ArrowRight':
                this.keys.right = false;
                break;
            case 'KeyQ':
                this.keys.up = false;
                break;
            case 'KeyE':
                this.keys.down = false;
                break;
        }
    }

    createFollowPath() {
        // 创建沿着工艺流程的路径
        const points = [
            new THREE.Vector3(-25, 5, 5),   // 格栅池
            new THREE.Vector3(-15, 5, 5),   // 沉砂池
            new THREE.Vector3(-5, 5, 5),    // 提升泵站
            new THREE.Vector3(5, 5, 5),     // 初沉池
            new THREE.Vector3(20, 8, 8),    // 生化池
            new THREE.Vector3(35, 6, 5),    // 二沉池
            new THREE.Vector3(50, 5, 5),    // 深度处理
            new THREE.Vector3(60, 5, 5)     // 出水
        ];

        this.followPath = new THREE.CatmullRomCurve3(points);
        this.followPath.closed = false;
    }

    changeViewMode(mode) {
        if (this.isAnimating || !this.presetPositions[mode]) {
            return;
        }

        this.currentMode = mode;
        
        if (mode === 'follow') {
            this.startFollowMode();
        } else {
            this.stopFollowMode();
            this.animateToPosition(this.presetPositions[mode]);
        }
    }

    animateToPosition(preset, duration = 2000) {
        if (this.isAnimating) return;

        this.isAnimating = true;
        this.controls.enabled = false;

        const startPosition = this.camera.position.clone();
        const startTarget = this.controls.target.clone();

        // 位置动画
        new TWEEN.Tween(startPosition)
            .to(preset.position, duration)
            .easing(TWEEN.Easing.Quadratic.InOut)
            .onUpdate(() => {
                this.camera.position.copy(startPosition);
            })
            .start();

        // 目标动画
        new TWEEN.Tween(startTarget)
            .to(preset.target, duration)
            .easing(TWEEN.Easing.Quadratic.InOut)
            .onUpdate(() => {
                this.controls.target.copy(startTarget);
                this.camera.lookAt(startTarget);
            })
            .onComplete(() => {
                this.isAnimating = false;
                this.controls.enabled = true;
                this.controls.update();
            })
            .start();
    }

    startFollowMode() {
        this.followProgress = 0;
        this.isFollowing = true;
        this.controls.enabled = false;
    }

    stopFollowMode() {
        this.isFollowing = false;
        this.controls.enabled = true;
    }

    updateFollowMode(deltaTime) {
        if (!this.isFollowing || !this.followPath) return;

        this.followProgress += this.followSpeed * deltaTime * 60; // 60fps基准
        
        if (this.followProgress >= 1) {
            this.followProgress = 0; // 循环
        }

        // 获取路径上的位置
        const position = this.followPath.getPoint(this.followProgress);
        this.camera.position.copy(position);

        // 计算前方一点作为观察目标
        const lookAheadProgress = Math.min(this.followProgress + 0.1, 1);
        const lookTarget = this.followPath.getPoint(lookAheadProgress);
        lookTarget.y = 0; // 看向地面设备
        
        this.camera.lookAt(lookTarget);
    }

    resetToOverview() {
        this.changeViewMode('overview');
    }

    toggleAutoRotate() {
        this.controls.autoRotate = !this.controls.autoRotate;
    }

    // 手动键盘移动
    updateKeyboardMovement(deltaTime) {
        if (this.isAnimating || this.isFollowing) return;

        const moveSpeed = 20 * deltaTime;
        const direction = new THREE.Vector3();

        if (this.keys.forward) {
            this.camera.getWorldDirection(direction);
            this.camera.position.addScaledVector(direction, moveSpeed);
        }
        
        if (this.keys.backward) {
            this.camera.getWorldDirection(direction);
            this.camera.position.addScaledVector(direction, -moveSpeed);
        }
        
        if (this.keys.left) {
            direction.setFromMatrixColumn(this.camera.matrix, 0);
            this.camera.position.addScaledVector(direction, -moveSpeed);
        }
        
        if (this.keys.right) {
            direction.setFromMatrixColumn(this.camera.matrix, 0);
            this.camera.position.addScaledVector(direction, moveSpeed);
        }
        
        if (this.keys.up) {
            this.camera.position.y += moveSpeed;
        }
        
        if (this.keys.down) {
            this.camera.position.y -= moveSpeed;
        }
    }

    // 聚焦到特定设备
    focusOnEquipment(equipment, distance = 15) {
        if (this.isAnimating) return;

        const equipmentPosition = equipment.position.clone();
        const cameraPosition = equipmentPosition.clone();
        
        // 计算相机位置（设备前方）
        cameraPosition.x += distance * 0.7;
        cameraPosition.y += distance * 0.5;
        cameraPosition.z += distance * 0.7;

        const preset = {
            position: cameraPosition,
            target: equipmentPosition
        };

        this.animateToPosition(preset, 1500);
    }

    // 沿设备巡视
    tourEquipment(equipmentList, duration = 3000) {
        if (this.isAnimating || !equipmentList.length) return;

        let currentIndex = 0;
        
        const visitNext = () => {
            if (currentIndex >= equipmentList.length) {
                this.resetToOverview();
                return;
            }

            const equipment = equipmentList[currentIndex];
            this.focusOnEquipment(equipment);
            
            setTimeout(() => {
                currentIndex++;
                visitNext();
            }, duration);
        };

        visitNext();
    }

    // 平滑缩放到指定距离
    zoomTo(distance, duration = 1000) {
        const currentDistance = this.camera.position.distanceTo(this.controls.target);
        const direction = this.camera.position.clone().sub(this.controls.target).normalize();
        const targetPosition = this.controls.target.clone().add(direction.multiplyScalar(distance));

        new TWEEN.Tween(this.camera.position)
            .to(targetPosition, duration)
            .easing(TWEEN.Easing.Quadratic.InOut)
            .onUpdate(() => {
                this.controls.update();
            })
            .start();
    }

    // 事件处理
    onControlStart() {
        // 用户开始交互时停止自动模式
        if (this.isFollowing) {
            this.stopFollowMode();
        }
    }

    onControlEnd() {
        // 交互结束时的处理
    }

    // 更新方法
    update(deltaTime) {
        this.updateKeyboardMovement(deltaTime);
        
        if (this.isFollowing) {
            this.updateFollowMode(deltaTime);
        }
        
        this.controls.update();
    }

    // 获取当前相机信息
    getCameraInfo() {
        return {
            mode: this.currentMode,
            position: this.camera.position.clone(),
            target: this.controls.target.clone(),
            distance: this.camera.position.distanceTo(this.controls.target),
            isAnimating: this.isAnimating,
            isFollowing: this.isFollowing,
            autoRotate: this.controls.autoRotate
        };
    }

    // 保存/恢复相机状态
    saveState() {
        return {
            position: this.camera.position.clone(),
            target: this.controls.target.clone(),
            mode: this.currentMode
        };
    }

    restoreState(state) {
        this.camera.position.copy(state.position);
        this.controls.target.copy(state.target);
        this.currentMode = state.mode;
        this.controls.update();
    }

    dispose() {
        this.controls.dispose();
        document.removeEventListener('keydown', this.onKeyDown);
        document.removeEventListener('keyup', this.onKeyUp);
    }
}
