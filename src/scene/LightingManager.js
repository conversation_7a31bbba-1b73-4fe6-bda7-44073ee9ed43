/**
 * 光照管理器 - 负责场景光照的创建和控制
 */

import * as THREE from 'three';

export class LightingManager {
    constructor(scene) {
        this.scene = scene;
        this.lights = {};
        this.shadowMapSize = 2048;
        this.timeOfDay = 12; // 默认中午12点
    }

    setupLighting() {
        this.createAmbientLight();
        this.createDirectionalLight();
        this.createPointLights();
        this.createSpotLights();
        this.createHemisphereLight();
    }

    createAmbientLight() {
        // 环境光 - 提供基础照明
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);
        this.lights.ambient = ambientLight;
    }

    createDirectionalLight() {
        // 方向光 - 模拟太阳光
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(50, 50, 25);
        directionalLight.target.position.set(0, 0, 0);
        
        // 启用阴影
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = this.shadowMapSize;
        directionalLight.shadow.mapSize.height = this.shadowMapSize;
        
        // 设置阴影相机范围
        const shadowCamera = directionalLight.shadow.camera;
        shadowCamera.near = 0.1;
        shadowCamera.far = 200;
        shadowCamera.left = -100;
        shadowCamera.right = 100;
        shadowCamera.top = 100;
        shadowCamera.bottom = -100;
        
        // 阴影偏移，减少阴影失真
        directionalLight.shadow.bias = -0.0001;
        directionalLight.shadow.normalBias = 0.02;
        
        this.scene.add(directionalLight);
        this.scene.add(directionalLight.target);
        this.lights.directional = directionalLight;

        // 方向光辅助器已移除，避免产生白色线条
    }

    createHemisphereLight() {
        // 半球光 - 模拟天空散射光
        const hemisphereLight = new THREE.HemisphereLight(0x87ceeb, 0x8b7355, 0.3);
        hemisphereLight.position.set(0, 50, 0);
        this.scene.add(hemisphereLight);
        this.lights.hemisphere = hemisphereLight;
    }

    createPointLights() {
        // 设备区域点光源
        const equipmentLights = [];
        
        // 格栅池照明
        const gratingLight = new THREE.PointLight(0xffffff, 0.5, 30);
        gratingLight.position.set(-20, 8, 0);
        gratingLight.castShadow = true;
        gratingLight.shadow.mapSize.width = 512;
        gratingLight.shadow.mapSize.height = 512;
        this.scene.add(gratingLight);
        equipmentLights.push(gratingLight);

        // 生化池照明
        const bioLight = new THREE.PointLight(0x00ff88, 0.6, 35);
        bioLight.position.set(25, 10, 0);
        bioLight.castShadow = true;
        bioLight.shadow.mapSize.width = 512;
        bioLight.shadow.mapSize.height = 512;
        this.scene.add(bioLight);
        equipmentLights.push(bioLight);

        // 二沉池照明
        const clarifierLight = new THREE.PointLight(0x88ccff, 0.5, 30);
        clarifierLight.position.set(40, 8, 0);
        clarifierLight.castShadow = true;
        clarifierLight.shadow.mapSize.width = 512;
        clarifierLight.shadow.mapSize.height = 512;
        this.scene.add(clarifierLight);
        equipmentLights.push(clarifierLight);

        // 深度处理照明
        const advancedLight = new THREE.PointLight(0xcc88ff, 0.5, 25);
        advancedLight.position.set(55, 8, 0);
        advancedLight.castShadow = true;
        advancedLight.shadow.mapSize.width = 512;
        advancedLight.shadow.mapSize.height = 512;
        this.scene.add(advancedLight);
        equipmentLights.push(advancedLight);

        this.lights.equipment = equipmentLights;
    }

    createSpotLights() {
        // 聚光灯用于重点照明
        const spotLights = [];

        // 控制室聚光灯
        const controlSpot = new THREE.SpotLight(0xffffff, 1, 50, Math.PI / 6, 0.5, 2);
        controlSpot.position.set(-40, 15, -15);
        controlSpot.target.position.set(-40, 0, -15);
        controlSpot.castShadow = true;
        controlSpot.shadow.mapSize.width = 1024;
        controlSpot.shadow.mapSize.height = 1024;
        this.scene.add(controlSpot);
        this.scene.add(controlSpot.target);
        spotLights.push(controlSpot);

        // 主要工艺区聚光灯
        const processSpot = new THREE.SpotLight(0xffffff, 0.8, 60, Math.PI / 4, 0.3, 1.5);
        processSpot.position.set(20, 25, 0);
        processSpot.target.position.set(20, 0, 0);
        processSpot.castShadow = true;
        processSpot.shadow.mapSize.width = 1024;
        processSpot.shadow.mapSize.height = 1024;
        this.scene.add(processSpot);
        this.scene.add(processSpot.target);
        spotLights.push(processSpot);

        this.lights.spot = spotLights;
    }

    // 时间控制方法
    setTimeOfDay(hour) {
        this.timeOfDay = hour;
        this.updateLightingForTime();
    }

    updateLightingForTime() {
        const hour = this.timeOfDay;
        const isDay = hour >= 6 && hour <= 18;
        const isDusk = (hour >= 17 && hour <= 19) || (hour >= 5 && hour <= 7);
        
        // 调整方向光（太阳）
        if (this.lights.directional) {
            const sun = this.lights.directional;
            
            if (isDay) {
                // 白天
                sun.intensity = 0.8;
                sun.color.setHex(0xffffff);
                
                // 根据时间调整太阳位置
                const sunAngle = ((hour - 6) / 12) * Math.PI;
                sun.position.set(
                    Math.cos(sunAngle) * 50,
                    Math.sin(sunAngle) * 50,
                    25
                );
            } else {
                // 夜晚
                sun.intensity = 0.1;
                sun.color.setHex(0x404080);
                sun.position.set(-50, -20, 25);
            }
            
            if (isDusk) {
                // 黄昏/黎明
                sun.color.setHex(0xffa500);
                sun.intensity = 0.4;
            }
        }

        // 调整环境光
        if (this.lights.ambient) {
            if (isDay) {
                this.lights.ambient.intensity = 0.4;
                this.lights.ambient.color.setHex(0x404040);
            } else {
                this.lights.ambient.intensity = 0.2;
                this.lights.ambient.color.setHex(0x202040);
            }
        }

        // 调整半球光
        if (this.lights.hemisphere) {
            if (isDay) {
                this.lights.hemisphere.intensity = 0.3;
                this.lights.hemisphere.skyColor.setHex(0x87ceeb);
                this.lights.hemisphere.groundColor.setHex(0x8b7355);
            } else {
                this.lights.hemisphere.intensity = 0.1;
                this.lights.hemisphere.skyColor.setHex(0x000033);
                this.lights.hemisphere.groundColor.setHex(0x001122);
            }
        }

        // 调整设备照明
        if (this.lights.equipment) {
            this.lights.equipment.forEach(light => {
                if (isDay) {
                    light.intensity = 0.3;
                } else {
                    light.intensity = 0.8;
                }
            });
        }

        // 调整聚光灯
        if (this.lights.spot) {
            this.lights.spot.forEach(light => {
                if (isDay) {
                    light.intensity = 0.5;
                } else {
                    light.intensity = 1.2;
                }
            });
        }
    }

    // 天气效果
    setWeatherLighting(weather) {
        switch (weather) {
            case 'sunny':
                this.setSunnyLighting();
                break;
            case 'cloudy':
                this.setCloudyLighting();
                break;
            case 'rainy':
                this.setRainyLighting();
                break;
            case 'stormy':
                this.setStormyLighting();
                break;
            case 'foggy':
                this.setFoggyLighting();
                break;
        }
    }

    setSunnyLighting() {
        if (this.lights.directional) {
            this.lights.directional.intensity = 1.0;
            this.lights.directional.color.setHex(0xffffff);
        }
        if (this.lights.ambient) {
            this.lights.ambient.intensity = 0.4;
        }
    }

    setCloudyLighting() {
        if (this.lights.directional) {
            this.lights.directional.intensity = 0.6;
            this.lights.directional.color.setHex(0xcccccc);
        }
        if (this.lights.ambient) {
            this.lights.ambient.intensity = 0.5;
        }
    }

    setRainyLighting() {
        if (this.lights.directional) {
            this.lights.directional.intensity = 0.4;
            this.lights.directional.color.setHex(0x888888);
        }
        if (this.lights.ambient) {
            this.lights.ambient.intensity = 0.6;
            this.lights.ambient.color.setHex(0x404060);
        }
    }

    setStormyLighting() {
        this.setRainyLighting();
        // TODO: 添加闪电效果
        this.addLightningEffect();
    }

    setFoggyLighting() {
        if (this.lights.directional) {
            this.lights.directional.intensity = 0.3;
            this.lights.directional.color.setHex(0xaaaaaa);
        }
        if (this.lights.ambient) {
            this.lights.ambient.intensity = 0.7;
            this.lights.ambient.color.setHex(0x606060);
        }
    }

    addLightningEffect() {
        // 闪电效果
        const lightning = () => {
            if (this.lights.ambient) {
                const originalIntensity = this.lights.ambient.intensity;
                this.lights.ambient.intensity = 2.0;
                this.lights.ambient.color.setHex(0xffffff);
                
                setTimeout(() => {
                    this.lights.ambient.intensity = originalIntensity;
                    this.lights.ambient.color.setHex(0x404060);
                }, 100);
            }
        };

        // 随机闪电
        const scheduleNextLightning = () => {
            const delay = Math.random() * 10000 + 5000; // 5-15秒
            setTimeout(() => {
                lightning();
                scheduleNextLightning();
            }, delay);
        };

        scheduleNextLightning();
    }

    // 设备特殊照明效果
    addEquipmentLighting(equipment, type) {
        switch (type) {
            case 'pump':
                this.addPumpLighting(equipment);
                break;
            case 'tank':
                this.addTankLighting(equipment);
                break;
            case 'treatment':
                this.addTreatmentLighting(equipment);
                break;
        }
    }

    addPumpLighting(equipment) {
        // 泵站运行指示灯
        const indicatorLight = new THREE.PointLight(0x00ff00, 0.5, 10);
        indicatorLight.position.copy(equipment.position);
        indicatorLight.position.y += 3;
        this.scene.add(indicatorLight);
        
        equipment.userData.indicatorLight = indicatorLight;
    }

    addTankLighting(equipment) {
        // 水池边缘照明
        const rimLight = new THREE.PointLight(0x0088ff, 0.3, 15);
        rimLight.position.copy(equipment.position);
        rimLight.position.y += 2;
        this.scene.add(rimLight);
        
        equipment.userData.rimLight = rimLight;
    }

    addTreatmentLighting(equipment) {
        // 处理设备UV灯效果
        const uvLight = new THREE.PointLight(0x8800ff, 0.4, 12);
        uvLight.position.copy(equipment.position);
        uvLight.position.y += 2;
        this.scene.add(uvLight);
        
        equipment.userData.uvLight = uvLight;
    }

    // 动态光照效果
    startDynamicLighting() {
        this.dynamicLightingActive = true;
        this.animateLights();
    }

    stopDynamicLighting() {
        this.dynamicLightingActive = false;
    }

    animateLights() {
        if (!this.dynamicLightingActive) return;

        const time = Date.now() * 0.001;

        // 设备灯光闪烁效果
        if (this.lights.equipment) {
            this.lights.equipment.forEach((light, index) => {
                const flicker = Math.sin(time * 2 + index) * 0.1 + 0.9;
                light.intensity = light.userData.baseIntensity * flicker;
            });
        }

        requestAnimationFrame(() => this.animateLights());
    }

    // 获取当前光照信息
    getLightingInfo() {
        return {
            timeOfDay: this.timeOfDay,
            directionalIntensity: this.lights.directional?.intensity || 0,
            ambientIntensity: this.lights.ambient?.intensity || 0,
            equipmentLightCount: this.lights.equipment?.length || 0,
            spotLightCount: this.lights.spot?.length || 0
        };
    }

    dispose() {
        // 清理光照资源
        Object.values(this.lights).forEach(light => {
            if (Array.isArray(light)) {
                light.forEach(l => {
                    if (l.dispose) l.dispose();
                    this.scene.remove(l);
                });
            } else {
                if (light.dispose) light.dispose();
                this.scene.remove(light);
            }
        });
        
        this.lights = {};
        this.dynamicLightingActive = false;
    }
}
