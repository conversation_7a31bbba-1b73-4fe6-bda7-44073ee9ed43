import { defineConfig } from 'vite';


export default defineConfig({
    // 基础配置
    base: './',
    
    // 开发服务器配置
    server: {
        port: 3000,
        host: true,
        open: true
    },
    
    // 构建配置
    build: {
        outDir: 'dist',
        assetsDir: 'assets',
        sourcemap: false,
        minify: 'esbuild',
        rollupOptions: {
            output: {
                manualChunks: {
                    'three': ['three'],
                    'tween': ['@tweenjs/tween.js']
                }
            }
        }
    },
    
    // 优化配置
    optimizeDeps: {
        include: ['three', '@tweenjs/tween.js']
    },
    
    // 预览服务器配置
    preview: {
        port: 4173,
        host: true
    },
    

});
