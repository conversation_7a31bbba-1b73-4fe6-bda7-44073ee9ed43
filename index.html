<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D污水处理工艺流程系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
        }

        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #canvas-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        /* 控制面板样式 */
        .panel {
            position: absolute;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
        }

        #controlPanel {
            top: 20px;
            left: 20px;
            width: 280px;
            max-height: 400px;
            overflow-y: auto;
        }



        #infoPanel {
            top: 20px;
            right: 20px;
            width: 380px;
            min-height: 450px;
            max-height: calc(100vh - 40px);
            display: none;
            overflow-y: auto;
        }

        .panel h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 18px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }

        .panel h4 {
            color: #34495e;
            margin: 15px 0 8px 0;
            font-size: 14px;
        }

        .panel button {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px 5px 5px 0;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .panel button:hover {
            background: linear-gradient(45deg, #2980b9, #1f5f8b);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .panel select {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            background: white;
        }

        .panel input[type="checkbox"] {
            margin-right: 8px;
        }

        .panel label {
            display: block;
            margin: 8px 0;
            color: #2c3e50;
            font-size: 13px;
        }

        /* 设备数据气泡样式 */
        .equipment-bubble {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            z-index: 1500;
            width: 350px;
            min-height: 450px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            display: none;
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            pointer-events: none;
        }

        .equipment-bubble.show {
            display: block;
            transform: translateX(0);
            opacity: 1;
            pointer-events: auto;
        }

        .bubble-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }

        .bubble-title {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
        }

        .bubble-close {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #7f8c8d;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .bubble-close:hover {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
        }

        .bubble-content {
            display: flex;
            flex-direction: column;
            gap: 20px;
            min-height: 380px;
        }

        .info-section {
            background: rgba(248, 249, 250, 0.8);
            border-radius: 8px;
            padding: 16px;
            border: 1px solid rgba(52, 152, 219, 0.1);
            min-height: 80px;
        }

        .section-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid rgba(52, 152, 219, 0.2);
        }

        .section-icon {
            font-size: 16px;
        }

        .section-title {
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
        }

        .basic-info-grid {
            display: grid;
            gap: 12px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(52, 152, 219, 0.1);
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-size: 12px;
            color: #7f8c8d;
            font-weight: 500;
        }

        .info-value {
            font-size: 13px;
            color: #2c3e50;
            font-weight: 600;
        }

        .status-running {
            color: #27ae60 !important;
        }

        .status-stopped {
            color: #e74c3c !important;
        }

        .parameters-grid {
            display: grid;
            gap: 10px;
            min-height: 180px;
        }

        .param-item {
            display: grid;
            grid-template-columns: 1fr auto auto;
            gap: 12px;
            align-items: center;
            padding: 12px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 6px;
            border-left: 3px solid #3498db;
            min-height: 45px;
        }

        .param-label {
            font-size: 11px;
            color: #34495e;
            font-weight: 500;
        }

        .param-value {
            font-size: 13px;
            color: #27ae60;
            font-weight: bold;
            text-align: right;
        }

        .param-unit {
            font-size: 10px;
            color: #7f8c8d;
            font-weight: normal;
            margin-left: 2px;
        }

        .param-status {
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 9px;
            font-weight: bold;
        }

        .detail-link {
            margin-top: 8px;
            text-align: center;
        }

        .link-text {
            font-size: 11px;
            color: #3498db;
            cursor: pointer;
            text-decoration: underline;
        }

        .link-text:hover {
            color: #2980b9;
        }

        .control-buttons {
            display: flex;
            gap: 12px;
            justify-content: space-between;
            margin-top: 8px;
        }

        .control-btn {
            flex: 1;
            padding: 12px 16px;
            border: none;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            min-height: 40px;
        }

        .start-btn {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
        }

        .start-btn:hover {
            background: linear-gradient(135deg, #229954, #27ae60);
            transform: translateY(-1px);
        }

        .stop-btn {
            background: linear-gradient(135deg, #e74c3c, #ec7063);
            color: white;
        }

        .stop-btn:hover {
            background: linear-gradient(135deg, #c0392b, #e74c3c);
            transform: translateY(-1px);
        }

        .reset-btn {
            background: linear-gradient(135deg, #f39c12, #f4d03f);
            color: white;
        }

        .reset-btn:hover {
            background: linear-gradient(135deg, #e67e22, #f39c12);
            transform: translateY(-1px);
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .status-normal {
            background: rgba(39, 174, 96, 0.2);
            color: #27ae60;
        }

        .status-warning {
            background: rgba(243, 156, 18, 0.2);
            color: #f39c12;
        }

        .status-danger {
            background: rgba(231, 76, 60, 0.2);
            color: #e74c3c;
        }

        /* 加载界面 */
        #loadingScreen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            color: white;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 18px;
            margin-bottom: 10px;
        }

        .loading-progress {
            font-size: 14px;
            opacity: 0.8;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .panel {
                width: calc(100vw - 40px) !important;
                max-width: 300px;
            }

            #controlPanel {
                top: 10px;
                left: 10px;
            }

            #infoPanel {
                top: 10px;
                right: 10px;
                width: calc(100vw - 20px) !important;
                max-width: 350px;
                min-height: 400px;
                max-height: calc(100vh - 20px);
            }

            .equipment-bubble {
                top: 10px !important;
                right: 10px !important;
                width: calc(100vw - 20px) !important;
                max-width: 350px !important;
                min-height: 400px !important;
                max-height: calc(100vh - 20px) !important;
            }
        }
    </style>
</head>
<body>
    <div id="container">
        <!-- 加载界面 -->
        <div id="loadingScreen">
            <div class="loading-spinner"></div>
            <div class="loading-text">正在加载3D污水处理系统...</div>
            <div class="loading-progress" id="loadingProgress">初始化中...</div>
        </div>

        <!-- 3D画布容器 -->
        <div id="canvas-container"></div>

        <!-- 控制面板 -->
        <div id="controlPanel" class="panel">
            <h3>🎛️ 系统控制</h3>
            <button id="startSystem">▶️ 启动系统</button>
            <button id="stopSystem">⏹️ 停止系统</button>
            <button id="resetView">🔄 重置视角</button>
            
            <h4>📷 视角切换</h4>
            <select id="viewMode">
                <option value="overview">🌐 全景视图</option>
                <option value="detail">🔍 细节视图</option>
                <option value="follow">🚶 跟随模式</option>
            </select>
            
            <h4>👁️ 显示选项</h4>
            <label><input type="checkbox" id="showParticles" checked> 💧 显示粒子效果</label>
            <label><input type="checkbox" id="showPipeline" checked> 🔧 显示管道</label>
            <label><input type="checkbox" id="showLabels" checked> 🏷️ 显示标签</label>
            <label><input type="checkbox" id="showServiceArea" checked> 🏪 显示服务区</label>
            <label><input type="checkbox" id="showStats"> 📊 显示性能统计</label>

            <h4>🚰 管道控制</h4>
            <label>管道透明度: <input type="range" id="pipeOpacity" min="0.1" max="1" step="0.1" value="0.3"></label>
            <label>水流透明度: <input type="range" id="waterOpacity" min="0.1" max="1" step="0.1" value="0.7"></label>
            <label>水流速度: <input type="range" id="flowSpeed" min="0.1" max="3" step="0.1" value="1"></label>
            <label><input type="checkbox" id="reverseFlow"> 🔄 反向流动</label>
            <label><input type="checkbox" id="pipelineParticles" checked> ✨ 管道粒子效果</label>
            
            <h4>🎵 音效控制</h4>
            <label><input type="checkbox" id="enableSound"> 🔊 启用音效</label>
            <label>🔉 音量: <input type="range" id="volumeControl" min="0" max="100" value="50"></label>
        </div>

        <!-- 设备数据气泡 -->
        <div id="equipmentBubble" class="equipment-bubble">
            <div class="bubble-header">
                <div class="bubble-title" id="bubbleTitle">设备数据</div>
                <button class="bubble-close" id="bubbleClose">×</button>
            </div>
            <div class="bubble-content" id="bubbleContent">
                <!-- 动态内容将在这里生成 -->
            </div>
        </div>

        <!-- 信息面板 -->
        <div id="infoPanel" class="panel">
            <h3 id="infoTitle">设备信息</h3>
            <div id="infoContent">
                <p>点击设备查看详细信息</p>
            </div>
        </div>

        <style>
            /* 信息面板内容样式 */
            #infoPanel .equipment-info {
                display: flex;
                flex-direction: column;
                gap: 16px;
            }

            #infoPanel .equipment-info h4 {
                display: flex;
                align-items: center;
                gap: 8px;
                margin: 16px 0 12px 0;
                padding-bottom: 8px;
                border-bottom: 1px solid rgba(52, 152, 219, 0.2);
                color: #2c3e50;
                font-size: 14px;
                font-weight: 600;
            }

            #infoPanel .equipment-info p {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin: 8px 0;
                padding: 8px 0;
                font-size: 13px;
                border-bottom: 1px solid rgba(52, 152, 219, 0.1);
            }

            #infoPanel .equipment-info p:last-child {
                border-bottom: none;
            }

            #infoPanel .equipment-info strong {
                color: #7f8c8d;
                font-weight: 500;
                font-size: 12px;
            }

            #infoPanel .status-running {
                color: #27ae60 !important;
                font-weight: bold;
            }

            #infoPanel .status-stopped {
                color: #e74c3c !important;
                font-weight: bold;
            }

            #infoPanel .parameter-list {
                background: rgba(248, 249, 250, 0.8);
                border-radius: 6px;
                padding: 12px;
                margin-top: 8px;
            }

            #infoPanel .control-buttons {
                display: flex;
                gap: 12px;
                justify-content: space-between;
                margin-top: 12px;
            }

            #infoPanel .control-buttons button {
                flex: 1;
                padding: 12px 16px;
                border: none;
                border-radius: 6px;
                font-size: 13px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.2s ease;
                min-height: 40px;
            }

            #infoPanel .btn-start {
                background: linear-gradient(135deg, #27ae60, #2ecc71);
                color: white;
            }

            #infoPanel .btn-start:hover {
                background: linear-gradient(135deg, #229954, #27ae60);
                transform: translateY(-1px);
            }

            #infoPanel .btn-stop {
                background: linear-gradient(135deg, #e74c3c, #ec7063);
                color: white;
            }

            #infoPanel .btn-stop:hover {
                background: linear-gradient(135deg, #c0392b, #e74c3c);
                transform: translateY(-1px);
            }

            #infoPanel .btn-reset {
                background: linear-gradient(135deg, #f39c12, #f4d03f);
                color: white;
            }

            #infoPanel .btn-reset:hover {
                background: linear-gradient(135deg, #e67e22, #f39c12);
                transform: translateY(-1px);
            }
        </style>
    </div>

    <script type="module" src="/src/main.js"></script>
</body>
</html>
